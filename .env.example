# Application URL --- Used for email verification and password reset links
NEXT_PUBLIC_APP_URL=""

# Auth Secret
AUTH_SECRET=""

# Database URL
DATABASE_URL=""

# Authentication (Required) secure by random string for JWT signing
# To generate go to wsl -> openssl rand -hex 32
JWT_SECRET=""

# Email Service (Required)
RESEND_API_KEY=""

# Clerk Key
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=""
CLERK_SECRET_KEY=""
CLERK_WEBHOOK_SECRET=""

# OAuth Providers
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Vercel Blob Storage
BLOB_READ_WRITE_TOKEN=""

# Xendit Payment Gateway
XENDIT_SECRET_KEY=""
XENDIT_PUBLIC_KEY=""
XENDIT_CALLBACK_TOKEN=""
XENDIT_WEBHOOK_URL=""