"use server";

import { z } from "zod";
import { LoginSchema } from "@/schemas/zod";
import { signIn } from "@/lib/auth";
import { AuthError } from "next-auth";
import { getUserByEmail } from "@/lib/user";
import { debugUserByEmail, debugPasswordCheck } from "@/lib/debug-auth";

export const login = async (values: z.infer<typeof LoginSchema>) => {
  console.log("🔐 LOGIN ATTEMPT STARTED");
  console.log("📧 Login values received:", {
    email: values.email,
    passwordLength: values.password?.length,
  });

  // Validasi input menggunakan Zod schema
  const validatedFields = LoginSchema.safeParse(values);
  // Return error jika validasi gagal
  if (!validatedFields.success) {
    console.log("❌ Validation failed:", validatedFields.error);
    return { error: "Input tidak valid!" };
  }

  // Destructure data yang sudah tervalidasi
  const { email, password } = validatedFields.data;
  console.log("✅ Validation passed for email:", email);

  // Check if user exists and is verified
  console.log("🔍 Looking up user by email:", email);

  // Run debug check
  const debugResult = await debugUserByEmail(email);
  console.log("🔍 DEBUG result:", debugResult);

  const existingUser = await getUserByEmail(email);
  console.log(
    "👤 User lookup result:",
    existingUser
      ? {
          id: existingUser.id,
          email: existingUser.email,
          emailVerified: existingUser.emailVerified,
          hasPassword: !!existingUser.password,
          role: existingUser.role,
        }
      : "User not found"
  );

  // Run password debug check if user exists
  if (existingUser && existingUser.password) {
    const passwordDebug = await debugPasswordCheck(email, password);
    console.log("🔑 Password DEBUG result:", passwordDebug);
  }

  if (!existingUser) {
    console.log("❌ User not found");
    return {
      error: "User tidak ditemukan!",
    };
  }

  // Temporarily bypass email verification for development
  if (!existingUser.emailVerified) {
    console.log("⚠️ Email not verified, but allowing login for development");
    // In production, uncomment the lines below:
    // return {
    //   error: "Akun belum terverifikasi!\nSilakan cek email untuk verifikasi.",
    // };
  }

  // Jika pengguna tidak ditemukan, lempar error
  if (!existingUser || !existingUser.password) {
    console.log("❌ User not found or no password set");
    return { error: "User tidak ditemukan atau password salah!" };
  }

  console.log("✅ User validation passed, proceeding to authentication");

  try {
    try {
      console.log("🔑 Attempting signIn with credentials");
      // Use signIn with redirect: false to prevent automatic redirects
      const result = await signIn("credentials", {
        redirect: false,
        email,
        password,
      });

      console.log("✅ SignIn successful, result:", result);
      // Return success with session refresh flag
      return {
        success: "Login berhasil!",
        redirectTo: "/dashboard",
        refreshSession: true,
      };
    } catch (error) {
      console.log("❌ SignIn error caught:", error);

      if (error instanceof AuthError) {
        console.log("🚫 AuthError type:", error.type);
        switch (error.type) {
          case "CredentialsSignin":
            console.log("❌ Credentials signin error");
            return { error: "Email atau password salah!" };
          default:
            console.log("❌ Other auth error:", error.type);
            return { error: "Ada yang salah!" };
        }
      }

      // Check if this is a redirect error (which is actually a success)
      if (error instanceof Error && error.message?.includes("NEXT_REDIRECT")) {
        console.log("✅ Redirect error detected (success)");
        return {
          success: "Login berhasil!",
          redirectTo: "/dashboard",
          refreshSession: true,
        };
      }

      console.error("❌ Unexpected error during login:", error);
      return { error: "Terjadi kesalahan saat login!" };
    }
  } catch (error) {
    console.error("Error during login:", error);
    return { error: "Terjadi kesalahan saat login!" };
  }
};
