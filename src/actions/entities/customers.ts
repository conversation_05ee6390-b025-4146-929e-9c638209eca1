"use server";

import { z } from "zod";
import { CustomerSchema } from "@/schemas/zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { auth } from "@/lib/auth"; // Import auth to get session
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { createCustomerAddedNotification } from "@/lib/create-system-notification";
import { generateCustomerId } from "@/lib/generate-id";

export const addCustomer = async (values: z.infer<typeof CustomerSchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = CustomerSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    name,
    firstName,
    middleName,
    lastName,
    contactName,
    phone,
    telephone,
    fax,
    email,
    identityType,
    identityNumber,
    NIK,
    NPWP,
    companyName,
    otherInfo,
    address,
    billingAddress,
    shippingAddress,
    sameAsShipping,
    bankName,
    bankBranch,
    accountHolder,
    accountNumber,
    notes,
  } = validatedFields.data;

  try {
    // 2. Generate custom ID for the customer
    const customId = await generateCustomerId();

    // 3. Create the customer in the database
    // If email is empty string, set it to null to avoid unique constraint issues
    const customer = await db.customer.create({
      data: {
        id: customId, // Use the generated custom ID as the primary key
        name,
        firstName,
        middleName,
        lastName,
        contactName,
        phone,
        telephone,
        fax,
        email: email === "" ? null : email, // Convert empty string to null
        identityType,
        identityNumber,
        NIK,
        NPWP,
        companyName,
        otherInfo,
        address,
        billingAddress,
        shippingAddress: sameAsShipping ? billingAddress : shippingAddress,
        sameAsShipping,
        bankName,
        bankBranch,
        accountHolder,
        accountNumber,
        notes,
        userId, // Associate with the current user
      },
    });

    // 3. Create a notification for the new customer
    await createCustomerAddedNotification(
      name,
      contactName || email || phone || "Tidak ada kontak"
    );

    // 4. Revalidate the customers page to show the new customer
    revalidatePath("/dashboard/customers");

    return { success: "Pelanggan berhasil ditambahkan!", customer };
  } catch (error) {
    console.error("Error adding customer:", error);

    // Handle specific Prisma errors
    if (error && typeof error === "object" && "code" in error) {
      if (error.code === "P2002") {
        // Unique constraint violation
        return {
          error:
            "Pelanggan dengan data yang sama sudah ada. Silakan periksa kembali data yang dimasukkan.",
        };
      }
    }

    return { error: "Gagal menambahkan pelanggan. Silakan coba lagi." };
  }
};

export const getCustomers = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Fetch all customers for the current user or their owner
    const customers = await db.customer.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { customers };
  } catch (error) {
    console.error("Error fetching customers:", error);
    return { error: "Gagal mengambil data pelanggan." };
  }
};

export const getCustomerById = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Fetch the customer by ID, ensuring it belongs to the current user or their owner
    const customer = await db.customer.findUnique({
      where: {
        id,
        userId,
      },
    });

    if (!customer) {
      return { error: "Pelanggan tidak ditemukan." };
    }

    return { customer };
  } catch (error) {
    console.error("Error fetching customer:", error);
    return { error: "Gagal mengambil data pelanggan." };
  }
};

export const updateCustomer = async (
  id: string,
  values: z.infer<typeof CustomerSchema>
) => {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = user.id;

  // 1. Validate input server-side
  const validatedFields = CustomerSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    name,
    firstName,
    middleName,
    lastName,
    contactName,
    phone,
    telephone,
    fax,
    email,
    identityType,
    identityNumber,
    NIK,
    NPWP,
    companyName,
    otherInfo,
    address,
    billingAddress,
    shippingAddress,
    sameAsShipping,
    bankName,
    bankBranch,
    accountHolder,
    accountNumber,
    notes,
  } = validatedFields.data;

  try {
    // 2. Update the customer in the database
    // If email is empty string, set it to null to avoid unique constraint issues
    const customer = await db.customer.update({
      where: {
        id,
        userId, // Ensure the customer belongs to the current user
      },
      data: {
        name,
        firstName,
        middleName,
        lastName,
        contactName,
        phone,
        telephone,
        fax,
        email: email === "" ? null : email, // Convert empty string to null
        identityType,
        identityNumber,
        NIK,
        NPWP,
        companyName,
        otherInfo,
        address,
        billingAddress,
        shippingAddress: sameAsShipping ? billingAddress : shippingAddress,
        sameAsShipping,
        bankName,
        bankBranch,
        accountHolder,
        accountNumber,
        notes,
      },
    });

    // 3. Revalidate the customers page to show the updated customer
    revalidatePath("/dashboard/customers");
    revalidatePath(`/dashboard/customers/detail/${id}`);

    return { success: "Pelanggan berhasil diperbarui!", customer };
  } catch (error) {
    console.error("Error updating customer:", error);
    return { error: "Gagal memperbarui pelanggan. Silakan coba lagi." };
  }
};

export const deleteCustomer = async (id: string) => {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = user.id;

  try {
    // Check if the customer is referenced in any services
    const services = await db.service.findMany({
      where: {
        customerId: id,
      },
      take: 1, // We only need to know if there are any, not how many
    });

    if (services.length > 0) {
      return {
        error:
          "Pelanggan tidak dapat dihapus karena digunakan dalam catatan servis. Hapus catatan servis terkait terlebih dahulu.",
      };
    }

    // Delete the customer from the database
    await db.customer.delete({
      where: {
        id,
        userId, // Ensure the customer belongs to the current user
      },
    });

    // Revalidate the customers page to remove the deleted customer
    revalidatePath("/dashboard/customers");

    return { success: "Pelanggan berhasil dihapus!" };
  } catch (error) {
    console.error("Error deleting customer:", error);
    return { error: "Gagal menghapus pelanggan. Silakan coba lagi." };
  }
};
