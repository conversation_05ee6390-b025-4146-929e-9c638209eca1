"use server";

import { z } from "zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { EnhancedProductSchema } from "@/components/pages/dashboard/products/new/types";

import { generateProductId } from "@/lib/generate-id";

export const addProduct = async (
  values: z.infer<typeof EnhancedProductSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = EnhancedProductSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    name,
    description,
    sku,
    barcode,
    price,
    wholesalePrice,
    cost,
    stock,
    image,
    categoryId,
    salePriceTaxRate,
    wholesalePriceTaxRate,
    costPriceTaxRate,
    weight,
    length,
    width,
    height,
    dimensions,
    unit,
    unitId,
    tags,
    isDraft,
    hasVariants,
    colorVariants,
  } = validatedFields.data;

  // Handle both individual dimension fields and nested dimensions object
  const finalLength = length ?? dimensions?.length ?? null;
  const finalWidth = width ?? dimensions?.width ?? null;
  const finalHeight = height ?? dimensions?.height ?? null;

  try {
    // 2. Create product in database with transaction to handle variants
    const product = await db.$transaction(async (tx) => {
      // Generate a custom product ID with company-specific sequence
      const customProductId = await generateProductId(userId);

      // Create the main product with custom ID
      // If SKU is empty or just whitespace, set it to null to avoid unique constraint issues
      const processedSku = sku && sku.trim() !== "" ? sku.trim() : null;

      const newProduct = await tx.product.create({
        data: {
          id: customProductId, // Use the custom ID
          name,
          description,
          sku: processedSku, // Use processed SKU value
          barcode: barcode || null, // Add barcode field
          price,
          wholesalePrice,
          cost,
          stock,
          image,
          salePriceTaxRate: salePriceTaxRate || 0,
          wholesalePriceTaxRate: wholesalePriceTaxRate || 0,
          costPriceTaxRate: costPriceTaxRate || 0,
          weight: weight || 0,
          length: finalLength,
          width: finalWidth,
          height: finalHeight,
          unit: unit || "Pcs", // Add unit field with default
          unitId: unitId || null, // Add unitId field
          tags: tags || [],
          userId: userId, // Add the userId
          categoryId: categoryId || null, // Add categoryId if provided
          isDraft: isDraft || false, // Add isDraft flag
          hasVariants: hasVariants || false, // Add hasVariants flag
        },
      });

      // If product has variants, create them
      if (hasVariants && colorVariants && colorVariants.length > 0) {
        // Create all variants for this product
        await Promise.all(
          colorVariants.map((variant) =>
            tx.productVariant.create({
              data: {
                sku: variant.sku || null,
                colorName: variant.colorName,
                colorCode: variant.colorCode,
                price: null, // No variant-specific pricing
                stock: 0, // Stock managed through purchases
                image: variant.image || null,
                productId: newProduct.id, // Link to the parent product
              },
            })
          )
        );
      }

      return newProduct;
    });

    // 3. Revalidate the products page cache
    revalidatePath("/dashboard/products"); // Revalidate the path to show the new product

    return {
      success: "Produk berhasil ditambahkan!",
      productId: product.id,
    };
  } catch (error) {
    console.error("Database Error:", error);
    // Handle specific errors like unique constraint violation if needed
    if (
      error instanceof Error &&
      "code" in error &&
      (error as any).code === "P2002"
    ) {
      // Assuming P2002 is the unique constraint violation code for your DB
      // The target for @@unique([userId, sku]) might be reported differently,
      // adjust if needed based on actual error messages.
      // Often it's reported as ['userId', 'sku'] or similar.
      // Let's keep the general message for now unless specific handling is required.
      if ((error as any).meta?.target?.includes("sku")) {
        return { error: "SKU sudah digunakan untuk pengguna ini!" };
      }
    }
    return { error: "Gagal menambahkan produk ke database." };
  }
};

export const updateProduct = async (
  id: string,
  values: z.infer<typeof EnhancedProductSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = EnhancedProductSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    name,
    description,
    sku,
    barcode,
    price,
    wholesalePrice,
    cost,
    stock,
    image,
    categoryId,
    salePriceTaxRate,
    wholesalePriceTaxRate,
    costPriceTaxRate,
    weight,
    length,
    width,
    height,
    dimensions,
    unit,
    unitId,
    tags,
    isDraft,
    hasVariants,
    colorVariants,
  } = validatedFields.data;

  // Handle both individual dimension fields and nested dimensions object
  const finalLength = length ?? dimensions?.length ?? null;
  const finalWidth = width ?? dimensions?.width ?? null;
  const finalHeight = height ?? dimensions?.height ?? null;

  try {
    // Check if product exists and belongs to this user
    // First try to find by ID with case-insensitive match if it starts with "prd-"
    let existingProduct = null;

    if (id.toLowerCase().startsWith("prd-")) {
      existingProduct = await db.product.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId,
        },
        include: {
          variants: true,
        },
      });
    }

    // If not found, try to find by exact ID
    if (!existingProduct) {
      existingProduct = await db.product.findFirst({
        where: {
          id,
          userId,
        },
        include: {
          variants: true,
        },
      });
    }

    if (!existingProduct) {
      return { error: "Produk tidak ditemukan!" };
    }

    // 2. Update product in database with transaction to handle variants
    await db.$transaction(async (tx) => {
      // If SKU is empty or just whitespace, set it to null to avoid unique constraint issues
      const processedSku = sku && sku.trim() !== "" ? sku.trim() : null;

      // Update the main product
      await tx.product.update({
        where: {
          id: existingProduct.id,
        },
        data: {
          name,
          description,
          sku: processedSku, // Use processed SKU value
          barcode: barcode || null, // Add barcode field
          price,
          wholesalePrice,
          cost,
          stock,
          image,
          salePriceTaxRate: salePriceTaxRate || 0,
          wholesalePriceTaxRate: wholesalePriceTaxRate || 0,
          costPriceTaxRate: costPriceTaxRate || 0,
          weight: weight || 0,
          length: finalLength,
          width: finalWidth,
          height: finalHeight,
          unit: unit || "Pcs", // Add unit field with default
          unitId: unitId || null, // Add unitId field
          tags: tags || [],
          categoryId: categoryId || null,
          isDraft: isDraft || false,
          hasVariants: hasVariants || false,
        },
      });

      // If product has variants, handle them
      if (hasVariants && colorVariants && colorVariants.length > 0) {
        // Delete existing variants
        if (existingProduct.variants.length > 0) {
          await tx.productVariant.deleteMany({
            where: {
              productId: existingProduct.id,
            },
          });
        }

        // Create new variants
        await Promise.all(
          colorVariants.map((variant) =>
            tx.productVariant.create({
              data: {
                sku: variant.sku || null,
                colorName: variant.colorName,
                colorCode: variant.colorCode,
                price: null, // No variant-specific pricing
                stock: 0, // Stock managed through purchases
                image: variant.image || null,
                productId: existingProduct.id, // Link to the parent product
              },
            })
          )
        );
      } else if (!hasVariants && existingProduct.variants.length > 0) {
        // If variants are disabled but product had variants, delete them
        await tx.productVariant.deleteMany({
          where: {
            productId: id,
          },
        });
      }
    });

    // 3. Revalidate the products page cache
    revalidatePath("/dashboard/products"); // Revalidate the products list
    revalidatePath(`/dashboard/products/${existingProduct.id}`); // Revalidate the product detail page
    revalidatePath(`/dashboard/products/detail/${existingProduct.id}`); // Revalidate the product detail page

    return {
      success: "Produk berhasil diperbarui!",
      productId: existingProduct.id,
    };
  } catch (error) {
    console.error("Database Error:", error);
    // Handle specific errors like unique constraint violation if needed
    if (
      error instanceof Error &&
      "code" in error &&
      (error as any).code === "P2002"
    ) {
      if ((error as any).meta?.target?.includes("sku")) {
        return { error: "SKU sudah digunakan untuk pengguna ini!" };
      }
    }
    return { error: "Gagal memperbarui produk." };
  }
};

export const deleteProduct = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Check if product exists and belongs to this user
    // First try to find by ID with case-insensitive match if it starts with "prd-"
    let existingProduct = null;

    if (id.toLowerCase().startsWith("prd-")) {
      existingProduct = await db.product.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId,
        },
      });
    }

    // If not found, try to find by exact ID
    if (!existingProduct) {
      existingProduct = await db.product.findFirst({
        where: {
          id,
          userId,
        },
      });
    }

    if (!existingProduct) {
      return { error: "Produk tidak ditemukan!" };
    }

    // Check if the product is referenced in any purchase items
    const purchaseItems = await db.purchaseItem.findMany({
      where: {
        productId: id,
      },
      take: 1, // We only need to know if there are any, not how many
    });

    if (purchaseItems.length > 0) {
      return {
        error:
          "Produk tidak dapat dihapus karena digunakan dalam catatan pembelian. Hapus catatan pembelian terkait terlebih dahulu atau edit pembelian untuk mengganti produk.",
      };
    }

    // Check if the product is referenced in any sale items
    const saleItems = await db.saleItem.findMany({
      where: {
        productId: id,
      },
      take: 1, // We only need to know if there are any, not how many
    });

    if (saleItems.length > 0) {
      return {
        error:
          "Produk tidak dapat dihapus karena digunakan dalam catatan penjualan. Hapus catatan penjualan terkait terlebih dahulu atau edit penjualan untuk mengganti produk.",
      };
    }

    // Delete the product variants first (if any)
    await db.productVariant.deleteMany({
      where: {
        productId: existingProduct.id,
      },
    });

    // Delete the product
    await db.product.delete({
      where: {
        id: existingProduct.id,
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");

    return { success: "Produk berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus produk. Silakan coba lagi." };
  }
};
