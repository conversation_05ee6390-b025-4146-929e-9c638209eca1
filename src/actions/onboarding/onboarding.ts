"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import bcrypt from "bcryptjs";
import { OnboardingFormData } from "@/components/pages/dashboard/welcome/types";

export interface OnboardingResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Save onboarding data to the database
 */
export async function saveOnboardingData(
  data: OnboardingFormData
): Promise<OnboardingResult> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return {
        success: false,
        error: "User not authenticated",
      };
    }

    const userId = session.user.id;

    // Hash the password
    const hashedPassword = await bcrypt.hash(data.password, 12);

    // Start a transaction to ensure data consistency
    const result = await db.$transaction(async (tx) => {
      // Update User model with basic info
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          phone: data.phoneNumber,
          password: hashedPassword,
          hasCompletedOnboarding: true,
        },
      });

      // Create or update AdditionalUserInfo
      const additionalInfo = await tx.additionalUserInfo.upsert({
        where: { userId },
        create: {
          userId,
          companyName: data.companyName,
          companyUsername: data.companyUsername,
          position: data.position,
          employeeCount: data.employeeCount,
          occupation: data.occupation,
          industry: data.industry,
          subscriptionPackage: data.subscriptionPackage,
          referralCode: data.referralCode || null,
        },
        update: {
          companyName: data.companyName,
          companyUsername: data.companyUsername,
          position: data.position,
          employeeCount: data.employeeCount,
          occupation: data.occupation,
          industry: data.industry,
          subscriptionPackage: data.subscriptionPackage,
          referralCode: data.referralCode || null,
        },
      });

      return { user: updatedUser, additionalInfo };
    });

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("Error saving onboarding data:", error);

    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes("Unique constraint")) {
        if (error.message.includes("companyUsername")) {
          return {
            success: false,
            error:
              "Username perusahaan sudah digunakan. Silakan pilih username lain.",
          };
        }
        if (error.message.includes("phone")) {
          return {
            success: false,
            error:
              "Nomor handphone sudah terdaftar. Silakan gunakan nomor lain.",
          };
        }
      }
    }

    return {
      success: false,
      error: "Terjadi kesalahan saat menyimpan data. Silakan coba lagi.",
    };
  }
}

/**
 * Check if user has completed onboarding
 */
export async function checkOnboardingStatus(): Promise<{
  hasCompleted: boolean;
  user?: any;
}> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { hasCompleted: false };
    }

    // Employees don't need to complete onboarding - they always have "completed" status
    if (session.user.isEmployee) {
      return { hasCompleted: true };
    }

    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        hasCompletedOnboarding: true,
        phone: true,
        additionalInfo: {
          select: {
            companyName: true,
            companyUsername: true,
            position: true,
            employeeCount: true,
            occupation: true,
            industry: true,
            subscriptionPackage: true,
            referralCode: true,
          },
        },
      },
    });

    if (!user) {
      return { hasCompleted: false };
    }

    return {
      hasCompleted: user.hasCompletedOnboarding,
      user,
    };
  } catch (error) {
    console.error("Error checking onboarding status:", error);
    return { hasCompleted: false };
  }
}

/**
 * Get user onboarding data for pre-filling forms
 */
export async function getUserOnboardingData(): Promise<OnboardingResult> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return {
        success: false,
        error: "User not authenticated",
      };
    }

    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        additionalInfo: true,
      },
    });

    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    // Transform data to match form structure
    const formData: Partial<OnboardingFormData> = {
      companyName: user.additionalInfo?.companyName || "",
      companyUsername: user.additionalInfo?.companyUsername || "",
      phoneNumber: user.phone || "",
      // Don't return password for security
      position: user.additionalInfo?.position || "",
      employeeCount: user.additionalInfo?.employeeCount || "",
      occupation: user.additionalInfo?.occupation || "",
      industry: user.additionalInfo?.industry || "",
      subscriptionPackage: user.additionalInfo?.subscriptionPackage || "",
      referralCode: user.additionalInfo?.referralCode || "",
    };

    return {
      success: true,
      data: formData,
    };
  } catch (error) {
    console.error("Error getting user onboarding data:", error);
    return {
      success: false,
      error: "Terjadi kesalahan saat mengambil data.",
    };
  }
}

/**
 * Validate company username availability
 */
export async function validateCompanyUsername(
  username: string
): Promise<{ available: boolean; error?: string }> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { available: false, error: "User not authenticated" };
    }

    // Check if username is already taken by another user
    const existingAdditionalInfo = await db.additionalUserInfo.findFirst({
      where: {
        companyUsername: username,
        NOT: {
          userId: session.user.id, // Exclude current user
        },
      },
    });

    return {
      available: !existingAdditionalInfo,
      error: existingAdditionalInfo ? "Username sudah digunakan" : undefined,
    };
  } catch (error) {
    console.error("Error validating company username:", error);
    return {
      available: false,
      error: "Terjadi kesalahan saat validasi username",
    };
  }
}
