"use server";

import { put, del } from "@vercel/blob";
import { auth } from "@/lib/auth";

export async function uploadPurchaseDocument(formData: FormData) {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }

  const userId = user.id;
  const file = formData.get("file") as File;

  if (!file) {
    return { error: "No file provided" };
  }

  try {
    // Create a unique filename with user ID prefix for organization
    const filename = `purchases/${userId}/${Date.now()}-${file.name.replace(/\s+/g, "-")}`;

    // Upload the file to Vercel Blob
    const blob = await put(filename, file, {
      access: "public",
      token: process.env.BLOB_READ_WRITE_TOKEN || "",
    });

    return { url: blob.url, success: true, filename: file.name };
  } catch (error) {
    console.error("Error uploading to Blob:", error);
    return { error: "Failed to upload document", success: false };
  }
}

export async function deletePurchaseDocument(url: string) {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }

  if (!url) {
    return { error: "No document URL provided" };
  }

  try {
    // Delete the file from Vercel Blob
    await del(url, {
      token: process.env.BLOB_READ_WRITE_TOKEN || "",
    });

    return { success: true, message: "Dokumen berhasil dihapus" };
  } catch (error) {
    console.error("Error deleting from Blob:", error);
    return { error: "Failed to delete document", success: false };
  }
}
