import { redirect } from "next/navigation";
import { checkOnboardingStatus } from "@/actions/onboarding/onboarding";
import { auth } from "@/lib/auth";

// Redirect to the appropriate page based on onboarding status
export default async function DashboardPage() {
  const session = await auth();

  // If user is an employee, always redirect to summaries (skip onboarding)
  if (session?.user?.isEmployee) {
    redirect("/dashboard/summaries");
  }

  const { hasCompleted } = await checkOnboardingStatus();

  if (!hasCompleted) {
    redirect("/dashboard/welcome");
  } else {
    redirect("/dashboard/summaries");
  }
}
