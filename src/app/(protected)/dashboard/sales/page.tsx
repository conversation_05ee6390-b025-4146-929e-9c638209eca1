import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SalesPage from "@/components/pages/dashboard/sales/sales";
import { getSales } from "@/actions/entities/sales";
import { Sale } from "@/components/pages/dashboard/sales/types";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";
import { Metadata } from "next";

// Type for the raw sales data from getSales
type RawSaleData = {
  id: string;
  saleDate: Date;
  totalAmount: number; // Already converted in getSales
  transactionNumber?: string | null;
  invoiceRef?: string | null;
  isDraft?: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  employeeId?: string | null;
  customerId?: string | null;
  customerRefNumber?: string | null;
  shippingAddress?: string | null;
  paymentDueDate?: Date | null;
  paymentTerms?: string | null;
  warehouseId?: string | null;
  warehouse?: {
    id: string;
    name: string;
  } | null;
  tags?: string[];
  memo?: string | null;
  lampiran?: any[]; // JsonValue[] from Prisma
  priceIncludesTax?: boolean;
  items: {
    id: string;
    quantity: number;
    priceAtSale: number; // Already converted in getSales
    createdAt: Date;
    updatedAt: Date;
    saleId: string;
    productId: string;
    // New discount and metadata fields
    discountPercentage?: number | null; // Already converted in getSales
    discountAmount?: number | null; // Already converted in getSales
    eventDiscountId?: string | null;
    eventDiscountName?: string | null;
    isWholesale?: boolean | null;
    unit?: string | null;
    tax?: string | null;
    product: {
      name: string;
    };
  }[];
  customer?: {
    id: string;
    name: string;
    email?: string | null;
    phone?: string | null;
    NIK?: string | null;
    NPWP?: string | null;
  } | null;
  user?: {
    id: string;
    name: string | null;
    username: string | null;
    additionalInfo?: {
      companyName: string | null;
      companyAddress: string | null;
      companyPhone: string | null;
      companyEmail: string | null;
    } | null;
  };
  Employee?: {
    id: string;
    name: string;
    employeeId: string;
    role: string;
  } | null;
};

// Function to transform raw sales data to Sale type
function transformSalesData(rawSales: RawSaleData[]): Sale[] {
  return rawSales.map((sale) => ({
    id: sale.id,
    saleDate: sale.saleDate.toISOString(),
    totalAmount: sale.totalAmount, // Already a number from getSales
    transactionNumber: sale.transactionNumber,
    invoiceRef: sale.invoiceRef,
    isDraft: sale.isDraft || false, // Add the missing isDraft field
    createdAt: sale.createdAt.toISOString(),
    updatedAt: sale.updatedAt.toISOString(),
    userId: sale.userId,
    employeeId: sale.employeeId,
    customerId: sale.customerId,
    customerRefNumber: sale.customerRefNumber || undefined,
    shippingAddress: sale.shippingAddress || undefined,
    paymentDueDate: sale.paymentDueDate || undefined,
    paymentTerms: sale.paymentTerms || undefined,
    warehouseId: sale.warehouseId || undefined,
    warehouse: sale.warehouse
      ? {
          id: sale.warehouse.id,
          name: sale.warehouse.name,
        }
      : undefined,
    tags: sale.tags || [],
    memo: sale.memo || undefined,
    // Transform lampiran from JsonValue[] to expected format
    lampiran: (sale.lampiran || []).map((attachment: any) => ({
      url: attachment?.url || "",
      filename: attachment?.filename || "",
    })),
    priceIncludesTax: sale.priceIncludesTax,
    items: sale.items.map((item) => ({
      id: item.id,
      quantity: item.quantity,
      priceAtSale: item.priceAtSale, // Already a number from getSales
      saleId: item.saleId,
      productId: item.productId,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
      // Discount fields are already numbers from getSales, just handle null to undefined
      discountPercentage: item.discountPercentage ?? undefined,
      discountAmount: item.discountAmount ?? undefined,
      eventDiscountId: item.eventDiscountId || undefined,
      eventDiscountName: item.eventDiscountName || undefined,
      isWholesale: item.isWholesale || false,
      unit: item.unit || undefined,
      tax: item.tax || undefined,
      product: {
        name: item.product.name,
      },
    })),
    customer: sale.customer
      ? {
          id: sale.customer.id,
          name: sale.customer.name,
          email: sale.customer.email || undefined,
          phone: sale.customer.phone || undefined,
          NIK: sale.customer.NIK || undefined,
          NPWP: sale.customer.NPWP || undefined,
        }
      : undefined,
    user: sale.user
      ? {
          id: sale.user.id,
          name: sale.user.name,
          username: sale.user.username,
        }
      : undefined,
    Employee: sale.Employee
      ? {
          id: sale.Employee.id,
          name: sale.Employee.name,
          employeeId: sale.Employee.employeeId,
          role: sale.Employee.role,
        }
      : null,
    // Handle company info from user's additionalInfo
    companyInfo: sale.user?.additionalInfo
      ? {
          companyName: sale.user.additionalInfo.companyName,
          companyAddress: sale.user.additionalInfo.companyAddress,
          companyPhone: sale.user.additionalInfo.companyPhone,
          companyEmail: sale.user.additionalInfo.companyEmail,
        }
      : null,
  }));
}

export const metadata: Metadata = {
  title: "Penjualan - KivaPOS",
  description: "Kelola transaksi penjualan",
};

// This is an async Server Component
export default async function Sales() {
  // Fetch sales data
  const { sales, error } = await getSales();

  // Transform the raw sales data to match the expected Sale type
  const transformedSales = sales ? transformSalesData(sales as any) : [];

  return (
    <DashboardLayout>
      <DatabaseErrorWrapper
        hasError={!!error}
        errorMessage={error}
        title="Gagal Memuat Data Penjualan"
        description="Terjadi masalah saat mengambil data penjualan dari database. Silakan refresh halaman untuk mencoba lagi."
      >
        {/* Pass the transformed sales to the client component */}
        <SalesPage sales={transformedSales} />
      </DatabaseErrorWrapper>
    </DashboardLayout>
  );
}
