import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import SupportSettings from "@/components/pages/dashboard/settings/support/support-settings";

const SupportSettingsPage = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  return (
    <DashboardLayout>
      <SettingsLayout>
        <SupportSettings />
      </SettingsLayout>
    </DashboardLayout>
  );
};

export default SupportSettingsPage;
