import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SummariesPage from "@/components/pages/dashboard/summaries/summaries";
import { OnboardingGuard } from "@/components/auth/onboarding-guard";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Ringkasan | KivaPOS",
  description: "Ringkasan bisnis dan analitik Anda",
};

const Summaries = async () => {
  return (
    <OnboardingGuard requireOnboarding={true}>
      <DashboardLayout>
        <SummariesPage />
      </DashboardLayout>
    </OnboardingGuard>
  );
};

export default Summaries;
