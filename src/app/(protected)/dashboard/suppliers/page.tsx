import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SuppliersPage from "@/components/pages/dashboard/suppliers/suppliers";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";
import { auth } from "@/lib/auth";
import { getSuppliers } from "@/actions/entities/suppliers";
import { redirect } from "next/navigation";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Supplier | KivaPOS",
  description: "Kelola data supplier Anda",
};

const Suppliers = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch suppliers for the current user
  const { suppliers, error } = await getSuppliers();

  return (
    <DashboardLayout>
      <DatabaseErrorWrapper
        hasError={!!error}
        errorMessage={error}
        title="Gagal Memuat Data Supplier"
        description="Terjadi masalah saat mengambil data supplier dari database. <PERSON><PERSON><PERSON> refresh halaman untuk mencoba lagi."
      >
        {/* Pass the fetched suppliers to the client component */}
        <SuppliersPage suppliers={suppliers || []} />
      </DatabaseErrorWrapper>
    </DashboardLayout>
  );
};

export default Suppliers;
