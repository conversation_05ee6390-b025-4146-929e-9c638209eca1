import React from "react";
import { notFound } from "next/navigation";
import { db } from "@/lib/prisma";
import { renderHorizontalTemplate1 } from "@/components/pages/dashboard/purchases/templates/HorizontalTemplate1";
import type { Purchase } from "@/components/pages/dashboard/purchases/types";

type PageProps = {
  params: Promise<{ id: string }>;
};

// This is an async Server Component for public purchase invoice
export default async function PublicPurchaseInvoice(props: PageProps) {
  // Get the id from params
  const params = await props.params;
  const id = params.id as string;

  // First, check if the purchase exists (regardless of public status)
  const purchaseExists = await db.purchase.findFirst({
    where: {
      OR: [{ id: id }, { transactionNumber: id }],
    },
    select: {
      id: true,
      isPublic: true,
      transactionNumber: true,
    },
  });

  // If purchase doesn't exist at all, return 404
  if (!purchaseExists) {
    notFound();
  }

  // If purchase exists but is not public, show access denied message
  if (!purchaseExists.isPublic) {
    return (
      <div
        style={{
          fontFamily: "Arial, sans-serif",
          margin: 0,
          padding: "40px 20px",
          backgroundColor: "#f8f9fa",
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            backgroundColor: "white",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
            textAlign: "center",
            maxWidth: "500px",
            width: "100%",
          }}
        >
          <div
            style={{
              fontSize: "48px",
              marginBottom: "20px",
            }}
          >
            🔒
          </div>
          <h1
            style={{
              color: "#dc3545",
              fontSize: "24px",
              marginBottom: "16px",
              fontWeight: "bold",
            }}
          >
            Akses Terbatas
          </h1>
          <p
            style={{
              color: "#6c757d",
              fontSize: "14px",
              lineHeight: "1.5",
              margin: "0",
              fontWeight: "bold",
            }}
          >
            Transaksi belum diijinkan di share oleh pemilik, harap hubungi
            pemilik akun.
          </p>
        </div>
      </div>
    );
  }

  // Fetch the complete purchase data with all includes
  const purchase = await db.purchase.findFirst({
    where: {
      AND: [
        {
          OR: [{ id: id }, { transactionNumber: id }],
        },
        { isPublic: true },
      ],
    },
    include: {
      items: {
        include: {
          product: true,
        },
      },
      supplier: true,
      user: {
        include: {
          additionalInfo: true, // Include company info
        },
      },
      warehouse: true,
    },
  });

  // This should not happen since we already checked, but just in case
  if (!purchase) {
    notFound();
  }

  // Transform the purchase data to include companyInfo
  const transformedPurchase = {
    ...purchase,
    totalAmount: Number(purchase.totalAmount), // Convert Decimal to number
    companyInfo: purchase.user?.additionalInfo
      ? {
          companyName: purchase.user.additionalInfo.companyName,
          companyAddress: purchase.user.additionalInfo.companyAddress,
          companyPhone: purchase.user.additionalInfo.companyPhone,
          companyEmail: purchase.user.additionalInfo.companyEmail,
        }
      : null,
    items: purchase.items.map((item) => ({
      ...item,
      costAtPurchase: Number(item.costAtPurchase), // Convert Decimal to number
      discountPercentage: item.discountPercentage
        ? Number(item.discountPercentage)
        : undefined,
      discountAmount: item.discountAmount
        ? Number(item.discountAmount)
        : undefined,
    })),
  } as unknown as Purchase;

  // Generate the HTML content using the horizontal template
  const htmlContent = renderHorizontalTemplate1(transformedPurchase);

  // Return the HTML content directly
  return (
    <div
      dangerouslySetInnerHTML={{ __html: htmlContent }}
      style={{
        fontFamily: "Arial, sans-serif",
        margin: 0,
        padding: 0,
        backgroundColor: "white",
      }}
    />
  );
}

// Generate metadata for the page
export async function generateMetadata(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  const id = params.id;

  const purchase = await db.purchase.findFirst({
    where: {
      AND: [
        {
          OR: [{ id: id }, { transactionNumber: id }],
        },
        { isPublic: true },
      ],
    },
    select: {
      transactionNumber: true,
      id: true,
    },
  });

  if (!purchase) {
    return {
      title: "Faktur Tidak Ditemukan",
      description:
        "Faktur pembelian tidak ditemukan atau tidak tersedia untuk umum",
    };
  }

  return {
    title: `Faktur Pembelian - ${purchase.transactionNumber || purchase.id.substring(0, 8)}`,
    description: `Faktur pembelian ${purchase.transactionNumber || purchase.id.substring(0, 8)}`,
  };
}
