"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Edit, Trash2, Power, PowerOff, Calendar, Package } from "lucide-react";
import { toast } from "sonner";
import {
  deleteEventDiscount,
  toggleEventDiscountStatus,
} from "@/actions/event-discounts";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { EventDiscount } from "./types";

interface EventDiscountTableProps {
  eventDiscounts: EventDiscount[];
  loading: boolean;
  onEdit: (discount: EventDiscount) => void;
  onRefresh: () => void;
}

const EventDiscountTable: React.FC<EventDiscountTableProps> = ({
  eventDiscounts,
  loading,
  onEdit,
  onRefresh,
}) => {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [togglingId, setTogglingId] = useState<string | null>(null);

  const handleDelete = async (id: string) => {
    try {
      setDeletingId(id);
      const result = await deleteEventDiscount(id);
      if (result.success) {
        toast.success(result.success);
        onRefresh();
      } else {
        toast.error(result.error || "Failed to delete event discount");
      }
    } catch (error) {
      console.error("Error deleting event discount:", error);
      toast.error("Failed to delete event discount");
    } finally {
      setDeletingId(null);
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      setTogglingId(id);
      const result = await toggleEventDiscountStatus(id);
      if (result.success) {
        toast.success(result.success);
        onRefresh();
      } else {
        toast.error(result.error || "Failed to toggle event discount status");
      }
    } catch (error) {
      console.error("Error toggling event discount status:", error);
      toast.error("Failed to toggle event discount status");
    } finally {
      setTogglingId(null);
    }
  };

  const isDiscountActive = (discount: EventDiscount) => {
    const now = new Date();
    return (
      discount.isActive &&
      new Date(discount.startDate) <= now &&
      new Date(discount.endDate) >= now
    );
  };

  const getStatusBadge = (discount: EventDiscount) => {
    if (!discount.isActive) {
      return <Badge variant="secondary">Nonaktif</Badge>;
    }

    const now = new Date();
    const startDate = new Date(discount.startDate);
    const endDate = new Date(discount.endDate);

    if (now < startDate) {
      return <Badge variant="outline">Akan Datang</Badge>;
    } else if (now > endDate) {
      return <Badge variant="destructive">Berakhir</Badge>;
    } else {
      return <Badge variant="default">Aktif</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (eventDiscounts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <Package className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Belum ada diskon event</h3>
        <p className="text-muted-foreground">
          Buat diskon event pertama Anda untuk mulai memberikan diskon pada
          produk.
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nama</TableHead>
            <TableHead>Diskon</TableHead>
            <TableHead>Periode</TableHead>
            <TableHead>Produk</TableHead>
            <TableHead>Penjualan</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Aksi</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {eventDiscounts.map((discount) => (
            <TableRow key={discount.id}>
              <TableCell>
                <div>
                  <div className="font-medium">{discount.name}</div>
                  {discount.description && (
                    <div className="text-sm text-muted-foreground">
                      {discount.description}
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline">{discount.discountPercentage}%</Badge>
              </TableCell>
              <TableCell>
                <div className="text-sm">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {format(new Date(discount.startDate), "dd MMM yyyy", {
                      locale: id,
                    })}
                  </div>
                  <div className="text-muted-foreground">
                    s/d{" "}
                    {format(new Date(discount.endDate), "dd MMM yyyy", {
                      locale: id,
                    })}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Package className="h-3 w-3" />
                  {discount._count.products}
                </div>
              </TableCell>
              <TableCell>{discount._count.sales}</TableCell>
              <TableCell>{getStatusBadge(discount)}</TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleToggleStatus(discount.id)}
                    disabled={togglingId === discount.id}
                    className="h-8 w-8"
                  >
                    {discount.isActive ? (
                      <PowerOff className="h-4 w-4" />
                    ) : (
                      <Power className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEdit(discount)}
                    className="h-8 w-8"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Hapus Diskon Event</AlertDialogTitle>
                        <AlertDialogDescription>
                          Apakah Anda yakin ingin menghapus diskon event "
                          {discount.name}"? Tindakan ini tidak dapat dibatalkan.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Batal</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(discount.id)}
                          disabled={deletingId === discount.id}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          {deletingId === discount.id
                            ? "Menghapus..."
                            : "Hapus"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default EventDiscountTable;
