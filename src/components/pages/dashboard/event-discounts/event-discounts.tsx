"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Percent, Calendar, Package, Users } from "lucide-react";
import { toast } from "sonner";
import { getEventDiscounts } from "@/actions/event-discounts";
import EventDiscountTable from "./EventDiscountTable";
import EventDiscountDialog from "./EventDiscountDialog";
import { EventDiscount, transformEventDiscounts } from "./types";
import DashboardLayout from "@/components/layout/dashboardlayout";
import Head from "next/head";

const EventDiscountManagement: React.FC = () => {
  const [eventDiscounts, setEventDiscounts] = useState<EventDiscount[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingDiscount, setEditingDiscount] = useState<EventDiscount | null>(
    null
  );

  // Fetch event discounts
  const fetchEventDiscounts = async () => {
    try {
      setLoading(true);
      const result = await getEventDiscounts();
      if (result.success && result.data) {
        // Transform the raw data to match our component interface
        const transformedData = transformEventDiscounts(result.data as any);
        setEventDiscounts(transformedData);
      } else {
        toast.error(result.error || "Failed to fetch event discounts");
      }
    } catch (error) {
      console.error("Error fetching event discounts:", error);
      toast.error("Failed to fetch event discounts");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEventDiscounts();
  }, []);

  const handleCreateNew = () => {
    setEditingDiscount(null);
    setDialogOpen(true);
  };

  const handleEdit = (discount: EventDiscount) => {
    setEditingDiscount(discount);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setEditingDiscount(null);
  };

  const handleSuccess = () => {
    fetchEventDiscounts();
    handleDialogClose();
  };

  // Calculate statistics
  const totalDiscounts = eventDiscounts.length;
  const activeDiscounts = eventDiscounts.filter((d) => d.isActive).length;
  const totalProductsWithDiscounts = eventDiscounts.reduce(
    (sum, d) => sum + d._count.products,
    0
  );
  const totalSalesWithDiscounts = eventDiscounts.reduce(
    (sum, d) => sum + d._count.sales,
    0
  );

  return (
    <DashboardLayout>
      <Head>
        <title>Diskon Event - KivaPOS</title>
      </Head>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Diskon Event</h1>
            <p className="text-muted-foreground">
              Kelola diskon event untuk produk Anda
            </p>
          </div>
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Buat Diskon Event
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Diskon
              </CardTitle>
              <Percent className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalDiscounts}</div>
              <p className="text-xs text-muted-foreground">
                {activeDiscounts} aktif
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Diskon Aktif
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeDiscounts}</div>
              <p className="text-xs text-muted-foreground">
                dari {totalDiscounts} total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Produk Terdiskon
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalProductsWithDiscounts}
              </div>
              <p className="text-xs text-muted-foreground">total produk</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Penjualan Terdiskon
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalSalesWithDiscounts}
              </div>
              <p className="text-xs text-muted-foreground">total transaksi</p>
            </CardContent>
          </Card>
        </div>

        {/* Event Discounts Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Diskon Event</CardTitle>
          </CardHeader>
          <CardContent>
            <EventDiscountTable
              eventDiscounts={eventDiscounts}
              loading={loading}
              onEdit={handleEdit}
              onRefresh={fetchEventDiscounts}
            />
          </CardContent>
        </Card>

        {/* Create/Edit Dialog */}
        <EventDiscountDialog
          open={dialogOpen}
          onClose={handleDialogClose}
          onSuccess={handleSuccess}
          editingDiscount={editingDiscount}
        />
      </div>
    </DashboardLayout>
  );
};

export default EventDiscountManagement;
