"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";

export interface ProductFilterState {
  category?: string;
  stockStatus?: "all" | "in-stock" | "low-stock" | "out-of-stock";
  priceRange?: {
    min?: number;
    max?: number;
  };
  tags?: string[];
  hasVariants?: boolean;
  isDraft?: boolean;
}

interface ProductFilterProps {
  filters: ProductFilterState;
  onFilterChange: (filters: ProductFilterState) => void;
  categories?: Array<{ id: string; name: string }>;
  availableTags?: string[];
}

export const ProductFilter: React.FC<ProductFilterProps> = ({
  filters,
  onFilterChange,
  categories = [],
  availableTags = [],
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<ProductFilterState>(filters);

  // Apply filters
  const applyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };

  // Reset filters
  const resetFilters = () => {
    const emptyFilters: ProductFilterState = {
      category: undefined,
      stockStatus: "all",
      priceRange: { min: undefined, max: undefined },
      tags: [],
      hasVariants: undefined,
      isDraft: undefined,
    };
    setLocalFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.stockStatus && filters.stockStatus !== "all") count++;
    if (filters.priceRange?.min || filters.priceRange?.max) count++;
    if (filters.tags && filters.tags.length > 0) count++;
    if (filters.hasVariants !== undefined) count++;
    if (filters.isDraft !== undefined) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Handle tag toggle
  const toggleTag = (tag: string) => {
    const currentTags = localFilters.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter((t) => t !== tag)
      : [...currentTags, tag];

    setLocalFilters((prev) => ({
      ...prev,
      tags: newTags,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer relative"
        >
          <FunnelIcon className="mr-2 h-5 w-5" />
          Filter
          {activeFilterCount > 0 && (
            <Badge
              variant="destructive"
              className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <AdjustmentsHorizontalIcon className="h-5 w-5" />
            Filter Produk
          </DialogTitle>
          <DialogDescription>
            Gunakan filter untuk mempersempit pencarian produk
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 p-2">
          {/* Category Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Kategori</Label>
            <Select
              value={localFilters.category || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  category: value === "all" ? undefined : value,
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih kategori" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Kategori</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Stock Status Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Status Stok</Label>
            <Select
              value={localFilters.stockStatus || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  stockStatus: value as any,
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status stok" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="in-stock">Tersedia</SelectItem>
                <SelectItem value="low-stock">Stok Menipis (≤5)</SelectItem>
                <SelectItem value="out-of-stock">Habis</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Price Range Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Rentang Harga</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs text-gray-500">Harga Minimum</Label>
                <Input
                  type="number"
                  placeholder="0"
                  value={localFilters.priceRange?.min || ""}
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      priceRange: {
                        ...prev.priceRange,
                        min: e.target.value
                          ? Number(e.target.value)
                          : undefined,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <Label className="text-xs text-gray-500">Harga Maksimum</Label>
                <Input
                  type="number"
                  placeholder="Tidak terbatas"
                  value={localFilters.priceRange?.max || ""}
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      priceRange: {
                        ...prev.priceRange,
                        max: e.target.value
                          ? Number(e.target.value)
                          : undefined,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Tags Filter */}
          {availableTags.length > 0 && (
            <>
              <div className="space-y-3">
                <Label className="text-sm font-medium">Tag Produk</Label>
                <div className="flex flex-wrap gap-2">
                  {availableTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={
                        localFilters.tags?.includes(tag) ? "default" : "outline"
                      }
                      className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                      onClick={() => toggleTag(tag)}
                    >
                      {tag}
                      {localFilters.tags?.includes(tag) && (
                        <XMarkIcon className="ml-1 h-3 w-3" />
                      )}
                    </Badge>
                  ))}
                </div>
              </div>
              <Separator />
            </>
          )}

          {/* Additional Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Opsi Lainnya</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasVariants"
                  checked={localFilters.hasVariants === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      hasVariants: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="hasVariants" className="text-sm cursor-pointer">
                  Memiliki varian warna
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isDraft"
                  checked={localFilters.isDraft === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      isDraft: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="isDraft" className="text-sm cursor-pointer">
                  Hanya produk draft
                </Label>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons - Fixed at bottom */}
        <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
          <Button variant="outline" onClick={resetFilters}>
            Reset Filter
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Batal
            </Button>
            <Button onClick={applyFilters}>Terapkan Filter</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
