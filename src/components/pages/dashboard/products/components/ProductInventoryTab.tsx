"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Layers, AlertCircle, Check, X, Package, Tag } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface ProductVariant {
  id: string;
  sku: string | null;
  colorName: string;
  colorCode: string;
  price: number | null;
  stock: number;
  image: string | null;
  productId: string;
}

interface ProductInventoryTabProps {
  stock: number;
  minStockLevel?: number;
  trackInventory?: boolean;
  hasVariants?: boolean;
  weight?: number | null;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  variants?: ProductVariant[];
}

const ProductInventoryTab: React.FC<ProductInventoryTabProps> = ({
  stock,
  minStockLevel,
  trackInventory,
  hasVariants,
  weight,
  dimensions,
  variants,
}) => {
  // Determine stock status for visual indicators
  const getStockStatus = () => {
    if (stock <= 0) return "empty";
    if (minStockLevel && stock <= minStockLevel) return "low";
    return "normal";
  };

  const stockStatus = getStockStatus();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Left Column */}
      <div className="space-y-6">
        {/* Stock Management Card - Modern Design */}
        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="pt-4 pb-2 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30 border-b">
            <CardTitle className="text-lg flex items-center gap-2">
              <Layers className="h-5 w-5 text-purple-500" />
              Manajemen Stok
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="flex flex-col space-y-6">
              {/* Stock Level with Visual Indicator */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Stok Saat Ini
                  </h3>
                  <Badge
                    variant={
                      stockStatus === "empty"
                        ? "destructive"
                        : stockStatus === "low"
                          ? "outline"
                          : "secondary"
                    }
                    className={
                      stockStatus === "low"
                        ? "bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800"
                        : ""
                    }
                  >
                    {stockStatus === "empty"
                      ? "Habis"
                      : stockStatus === "low"
                        ? "Stok Rendah"
                        : "Tersedia"}
                  </Badge>
                </div>
                <div className="flex items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                      stockStatus === "empty"
                        ? "bg-red-100 dark:bg-red-900/30"
                        : stockStatus === "low"
                          ? "bg-amber-100 dark:bg-amber-900/30"
                          : "bg-green-100 dark:bg-green-900/30"
                    }`}
                  >
                    {stockStatus === "empty" ? (
                      <AlertCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
                    ) : stockStatus === "low" ? (
                      <AlertCircle className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    ) : (
                      <Check className="h-6 w-6 text-green-600 dark:text-green-400" />
                    )}
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {stock}
                      <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                        {" "}
                        unit
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              {/* Inventory Settings */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
                  Pengaturan Inventaris
                </h3>
                <div className="space-y-3">
                  {/* Minimum Stock Level */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                        <Package className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <span className="text-sm">Stok Minimum</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">
                        {minStockLevel !== undefined ? minStockLevel : 0}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                        unit
                      </span>
                    </div>
                  </div>

                  <Separator />

                  {/* Track Inventory */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mr-3">
                        <Tag className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <span className="text-sm">Lacak Inventaris</span>
                    </div>
                    <div className="flex items-center">
                      {trackInventory ? (
                        <Badge className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800">
                          <Check className="h-3 w-3 mr-1" /> Aktif
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-gray-500">
                          <X className="h-3 w-3 mr-1" /> Nonaktif
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information Card - Improved UI */}
        {(weight ||
          (dimensions &&
            (dimensions.length || dimensions.width || dimensions.height))) && (
          <Card className="overflow-hidden border-none shadow-md">
            <CardHeader className="pt-4 pb-2 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 border-b">
              <CardTitle className="text-lg flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5 text-amber-500"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 6v6l4 2" />
                </svg>
                Informasi Lainnya
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {/* Weight Section */}
                {weight && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-5 w-5 text-amber-600 dark:text-amber-400"
                        >
                          <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                          <circle cx="12" cy="10" r="3" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Berat Produk
                        </p>
                        <div className="flex items-baseline gap-1">
                          <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                            {weight}
                          </span>
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            gram
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Dimensions Section */}
                {dimensions &&
                  (dimensions.length ||
                    dimensions.width ||
                    dimensions.height) && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-5 w-5 text-orange-600 dark:text-orange-400"
                          >
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96" />
                            <line x1="12" y1="22.08" x2="12" y2="12" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Dimensi Produk
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Panjang × Lebar × Tinggi
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-800">
                            <p className="text-xs font-medium text-orange-600 dark:text-orange-400 mb-1">
                              Panjang
                            </p>
                            <div className="flex items-baseline justify-center gap-1">
                              <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                {dimensions.length || 0}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                cm
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-800">
                            <p className="text-xs font-medium text-orange-600 dark:text-orange-400 mb-1">
                              Lebar
                            </p>
                            <div className="flex items-baseline justify-center gap-1">
                              <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                {dimensions.width || 0}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                cm
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-800">
                            <p className="text-xs font-medium text-orange-600 dark:text-orange-400 mb-1">
                              Tinggi
                            </p>
                            <div className="flex items-baseline justify-center gap-1">
                              <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                {dimensions.height || 0}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                cm
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Volume Calculation */}
                      {dimensions.length &&
                        dimensions.width &&
                        dimensions.height && (
                          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">
                                Volume:
                              </span>
                              <span className="font-medium">
                                {(
                                  dimensions.length *
                                  dimensions.width *
                                  dimensions.height
                                ).toLocaleString()}{" "}
                                cm³
                              </span>
                            </div>
                          </div>
                        )}
                    </div>
                  )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Right Column */}
      <div className="space-y-6">
        {/* Color Variants Card - Improved UI */}
        {variants && variants.length > 0 && (
          <Card className="overflow-hidden border-none shadow-md">
            <CardHeader className="pt-4 pb-2 bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/30 dark:to-teal-950/30 border-b">
              <CardTitle className="text-lg flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5 text-emerald-500"
                >
                  <circle cx="12" cy="12" r="3" />
                  <path d="M12 1v6m0 6v6" />
                  <path d="m9 9 3 3 3-3" />
                  <path d="m9 15 3-3 3 3" />
                  <path d="M1 12h6m6 0h6" />
                </svg>
                Varian Warna
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {variants.map((variant, index) => (
                  <div
                    key={variant.id || index}
                    className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="flex items-center gap-4">
                      {/* Color Circle */}
                      <div className="relative">
                        <div
                          className="w-10 h-10 rounded-full border-3 border-white shadow-lg ring-2 ring-gray-200 dark:ring-gray-600"
                          style={{ backgroundColor: variant.colorCode }}
                        ></div>
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full border-2 border-white flex items-center justify-center">
                          <Check className="h-2 w-2 text-white" />
                        </div>
                      </div>

                      {/* Variant Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-gray-900 dark:text-gray-100 truncate">
                            {variant.colorName}
                          </h4>
                          <Badge
                            variant="secondary"
                            className="text-xs bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400"
                          >
                            {variant.colorCode}
                          </Badge>
                        </div>
                        {variant.sku && (
                          <p className="text-sm text-muted-foreground font-mono">
                            SKU: {variant.sku}
                          </p>
                        )}
                      </div>

                      {/* Stock Info */}
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-gray-400" />
                          <span className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                            {variant.stock}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          unit tersedia
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Summary */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Total Varian:</span>
                  <span className="font-medium">{variants.length} warna</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-1">
                  <span className="text-muted-foreground">
                    Total Stok Varian:
                  </span>
                  <span className="font-medium">
                    {variants.reduce(
                      (total, variant) => total + variant.stock,
                      0
                    )}{" "}
                    unit
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProductInventoryTab;
