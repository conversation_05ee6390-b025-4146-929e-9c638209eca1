"use client";

import React, { useState, useEffect } from "react";
import { Tabs } from "@/components/ui/vercel-tabs";
import { WarehouseManagement } from "../../warehouses/components/WarehouseManagement";
import { WarehouseStockOverview } from "../../warehouses/components/WarehouseStockOverview";
import { StockMovementHistory } from "../../warehouses/components/StockMovementHistory";

export const WarehouseTabContent: React.FC = () => {
  const [activeTab, setActiveTab] = useState("warehouses");

  return (
    <div className="space-y-6">
      <Tabs
        tabs={[
          { id: "warehouses", label: "Daftar Gudang" },
          { id: "stock", label: "Stok Gudang" },
          { id: "movements", label: "Riwayat Pergerakan" },
        ]}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        className="w-full"
      />

      {activeTab === "warehouses" && (
        <div className="space-y-6">
          <WarehouseManagement />
        </div>
      )}

      {activeTab === "stock" && (
        <div className="space-y-6">
          <WarehouseStockOverview />
        </div>
      )}

      {activeTab === "movements" && (
        <div className="space-y-6">
          <StockMovementHistory />
        </div>
      )}
    </div>
  );
};
