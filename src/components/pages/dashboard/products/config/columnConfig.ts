import { ColumnVisibility } from "../types";

export interface ColumnConfig {
  key: keyof ColumnVisibility;
  label: string;
  sortKey: string;
}

// Products column configuration with order (matching the actual table structure)
export const productsColumnConfig: ColumnConfig[] = [
  { key: "image", label: "Gambar Produk", sortKey: "image" },
  { key: "name", label: "Nama Produk", sortKey: "name" },
  { key: "sku", label: "Kode Produk", sortKey: "sku" },
  { key: "barcode", label: "Barcode", sortKey: "barcode" },
  { key: "unit", label: "Satuan", sortKey: "unit" },
  { key: "stock", label: "Total Stok", sortKey: "stock" },
  { key: "cost", label: "Harga Beli", sortKey: "cost" },
  { key: "sellPrice", label: "Harga Jual", sortKey: "price" },
  { key: "wholesalePrice", label: "Harga Grosir", sortKey: "wholesalePrice" },
  { key: "discountPrice", label: "Harga Diskon", sortKey: "discountPrice" },
  { key: "category", label: "Kategori Produk", sortKey: "category" },
  { key: "tags", label: "Tag Produk", sortKey: "tags" },
  { key: "colorVariants", label: "Varian Warna", sortKey: "colorVariants" },
  { key: "stockStatus", label: "Status Stok", sortKey: "stockStatus" },
];
