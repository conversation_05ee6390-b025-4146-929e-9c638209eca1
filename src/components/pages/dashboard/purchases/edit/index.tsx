"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  updatePurchase,
  getNextTransactionNumber,
} from "@/actions/entities/purchases";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  PurchaseFormValues,
  Product,
  Supplier,
  EnhancedPurchaseSchema,
} from "../new/types";
import CombinedPurchaseForm from "../new/components/CombinedPurchaseForm";
import { ArrowLeft, Check, Save } from "lucide-react";

// Define the Purchase interface
interface PurchaseItem {
  id: string;
  quantity: number;
  costAtPurchase: number;
  productId: string;
  product: Product;
  unit?: string;
  tax?: string | null;
  discountPercentage?: number | null;
  discountAmount?: number | null;
}

interface Purchase {
  id: string;
  totalAmount: number;
  invoiceRef: string | null;
  purchaseDate: string;
  supplierId: string | null;
  items: PurchaseItem[];
  isDraft: boolean;
  supplierEmail?: string | null;
  transactionDate?: string;
  paymentDueDate?: string | null;
  transactionNumber?: string | null;
  tags?: string[];
  billingAddress?: string | null;
  // Warehouse relationship
  warehouseId?: string | null;
  warehouse?: {
    id: string;
    name: string;
  } | null;
  // Supplier relationship
  supplier?: {
    id: string;
    name: string;
    phone?: string | null;
  } | null;

  memo?: string | null;
  lampiran?: Array<{ url: string; filename: string }>;
  createdAt: string; // Add createdAt field
}

// Props use imported types
interface EnhancedPurchaseEditPageProps {
  purchase: Purchase;
  products: Product[];
  suppliers: Supplier[];
}

const EnhancedPurchaseEditPage: React.FC<EnhancedPurchaseEditPageProps> = ({
  purchase,
  products,
  suppliers,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(purchase.totalAmount);

  // Calculate existing discount values from purchase items
  const calculateExistingDiscount = () => {
    // Calculate subtotal before discount
    const subtotalBeforeDiscount = purchase.items.reduce((sum, item) => {
      return sum + item.quantity * item.costAtPurchase;
    }, 0);

    // Check if any items have discount values
    const itemsWithDiscount = purchase.items.filter(
      (item) =>
        (item.discountPercentage && item.discountPercentage > 0) ||
        (item.discountAmount && item.discountAmount > 0)
    );

    if (itemsWithDiscount.length === 0) {
      return { globalDiscountPercentage: 0, globalDiscountAmount: 0 };
    }

    // Calculate total discount amount from all items
    const totalDiscountAmount = purchase.items.reduce((sum, item) => {
      if (item.discountPercentage && item.discountPercentage > 0) {
        // If item has percentage discount, calculate the amount
        return (
          sum +
          item.quantity * item.costAtPurchase * (item.discountPercentage / 100)
        );
      } else if (item.discountAmount && item.discountAmount > 0) {
        // If item has amount discount, use it directly
        return sum + item.discountAmount;
      }
      return sum;
    }, 0);

    // Check if all items have the same discount percentage
    const firstItemDiscountPercentage =
      itemsWithDiscount[0]?.discountPercentage || 0;
    const allSamePercentage = itemsWithDiscount.every(
      (item) => (item.discountPercentage || 0) === firstItemDiscountPercentage
    );

    if (allSamePercentage && firstItemDiscountPercentage > 0) {
      // If all items have the same percentage, use that as global percentage
      return {
        globalDiscountPercentage: firstItemDiscountPercentage,
        globalDiscountAmount: 0,
      };
    } else {
      // Otherwise, use the total discount amount
      return {
        globalDiscountPercentage: 0,
        globalDiscountAmount: totalDiscountAmount,
      };
    }
  };

  const existingDiscount = calculateExistingDiscount();

  // Initialize the form with enhanced schema and purchase data
  const form = useForm<PurchaseFormValues>({
    resolver: zodResolver(EnhancedPurchaseSchema),
    defaultValues: {
      items: purchase.items.map((item) => ({
        productId: item.productId,
        quantity: item.quantity,
        costAtPurchase: item.costAtPurchase,
        unit: item.unit || "Buah",
        tax: item.tax || "",
        // Include item-level discount fields for form compatibility
        discountPercentage: item.discountPercentage || 0,
        discountAmount: item.discountAmount || 0,
      })),
      totalAmount: purchase.totalAmount,
      invoiceRef: purchase.invoiceRef || "",
      supplierId: purchase.supplierId || "",
      paymentStatus: "paid",
      trackDelivery: false,
      notifyOnArrival: false,
      isDraft: false,
      // New fields
      supplierEmail: purchase.supplierEmail || "",
      supplierPhone: purchase.supplier?.phone || "", // Load supplier phone from relationship
      transactionDate: purchase.transactionDate
        ? new Date(purchase.transactionDate)
        : new Date(),
      paymentDueDate: purchase.paymentDueDate
        ? new Date(purchase.paymentDueDate)
        : undefined,
      transactionNumber: purchase.transactionNumber || "",
      tags: purchase.tags || [],
      billingAddress: purchase.billingAddress || "",
      warehouseId: purchase.warehouseId || "",

      memo: purchase.memo || "",
      lampiran: purchase.lampiran || [],
      priceIncludesTax: false,
      deliveryDate: undefined,
      // Global discount fields - load from existing purchase data
      globalDiscountPercentage: existingDiscount.globalDiscountPercentage,
      globalDiscountAmount: existingDiscount.globalDiscountAmount,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");

  // Calculate total amount whenever items or discount change
  useEffect(() => {
    const priceIncludesTax = form.watch("priceIncludesTax");
    const globalDiscountPercentage =
      form.watch("globalDiscountPercentage") || 0;
    const globalDiscountAmount = form.watch("globalDiscountAmount") || 0;

    // Calculate subtotal before discount
    const subtotalBeforeDiscount = items.reduce(
      (sum: number, item: PurchaseFormValues["items"][number]) => {
        const quantity = item?.quantity ?? 0;
        const cost = item?.costAtPurchase ?? 0;
        return sum + quantity * cost;
      },
      0
    );

    // Calculate global discount
    const globalDiscount =
      globalDiscountPercentage > 0
        ? subtotalBeforeDiscount * (globalDiscountPercentage / 100)
        : globalDiscountAmount;

    // Calculate subtotal after discount
    const subtotalAfterDiscount = Math.max(
      0,
      subtotalBeforeDiscount - globalDiscount
    );

    // Calculate tax on the discounted amount
    const taxAmount = items.reduce(
      (sum: number, item: PurchaseFormValues["items"][number]) => {
        const quantity = item?.quantity ?? 0;
        const cost = item?.costAtPurchase ?? 0;
        const taxRate = parseFloat(item?.tax || "0") / 100;

        // Calculate item's proportion of the total before discount
        const itemProportion =
          subtotalBeforeDiscount > 0
            ? (quantity * cost) / subtotalBeforeDiscount
            : 0;

        // Apply discount proportionally to this item
        const itemAfterDiscount =
          quantity * cost - globalDiscount * itemProportion;

        // Calculate tax on the discounted amount
        if (priceIncludesTax) {
          // If price includes tax, tax is already included
          return sum;
        } else {
          // If price doesn't include tax, add tax to the discounted amount
          return sum + itemAfterDiscount * taxRate;
        }
      },
      0
    );

    const total = priceIncludesTax
      ? subtotalAfterDiscount
      : subtotalAfterDiscount + taxAmount;
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists
    if (selectedProduct) {
      // Set cost if available
      if (typeof selectedProduct.cost === "number") {
        const costValue = selectedProduct.cost; // Assign to variable to help TS inference
        form.setValue(`items.${index}.costAtPurchase`, costValue);
      }

      // Set unit from product
      if (selectedProduct.unit) {
        form.setValue(`items.${index}.unit`, selectedProduct.unit);
      }

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        if (selectedProduct.cost) {
          currentItems[index].costAtPurchase = selectedProduct.cost;
        }
        if (selectedProduct.unit) {
          currentItems[index].unit = selectedProduct.unit;
        }
      }

      const priceIncludesTax = form.watch("priceIncludesTax");
      const globalDiscountPercentage =
        form.watch("globalDiscountPercentage") || 0;
      const globalDiscountAmount = form.watch("globalDiscountAmount") || 0;

      // Calculate subtotal before discount
      const subtotalBeforeDiscount = currentItems.reduce(
        (sum: number, item: PurchaseFormValues["items"][number]) => {
          const quantity = item?.quantity ?? 0;
          const cost = item?.costAtPurchase ?? 0;
          return sum + quantity * cost;
        },
        0
      );

      // Calculate global discount
      const globalDiscount =
        globalDiscountPercentage > 0
          ? subtotalBeforeDiscount * (globalDiscountPercentage / 100)
          : globalDiscountAmount;

      // Calculate subtotal after discount
      const subtotalAfterDiscount = Math.max(
        0,
        subtotalBeforeDiscount - globalDiscount
      );

      // Calculate tax on the discounted amount
      const taxAmount = currentItems.reduce(
        (sum: number, item: PurchaseFormValues["items"][number]) => {
          const quantity = item?.quantity ?? 0;
          const cost = item?.costAtPurchase ?? 0;
          const taxRate = parseFloat(item?.tax || "0") / 100;

          // Calculate item's proportion of the total before discount
          const itemProportion =
            subtotalBeforeDiscount > 0
              ? (quantity * cost) / subtotalBeforeDiscount
              : 0;

          // Apply discount proportionally to this item
          const itemAfterDiscount =
            quantity * cost - globalDiscount * itemProportion;

          // Calculate tax on the discounted amount
          if (priceIncludesTax) {
            // If price includes tax, tax is already included
            return sum;
          } else {
            // If price doesn't include tax, add tax to the discounted amount
            return sum + itemAfterDiscount * taxRate;
          }
        },
        0
      );

      const total = priceIncludesTax
        ? subtotalAfterDiscount
        : subtotalAfterDiscount + taxAmount;

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Handle form submission
  const onSubmit = (values: PurchaseFormValues) => {
    startTransition(async () => {
      try {
        // Generate auto values if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        // If transaction number is empty, generate one using the purchase's createdAt date
        if (!values.transactionNumber) {
          autoTransactionNumber = await getNextTransactionNumber(
            "TRX",
            purchase.createdAt
          );
        }

        // If invoice reference is empty, generate one using the purchase's createdAt date
        if (!values.invoiceRef) {
          autoInvoiceRef = await getNextTransactionNumber(
            "INV",
            purchase.createdAt
          );
        }

        // Calculate subtotal before discount for proportional distribution
        const subtotalBeforeDiscount = values.items.reduce((sum, item) => {
          return sum + item.quantity * item.costAtPurchase;
        }, 0);

        // Calculate global discount amount
        const globalDiscountAmount =
          values.globalDiscountPercentage > 0
            ? subtotalBeforeDiscount * (values.globalDiscountPercentage / 100)
            : values.globalDiscountAmount || 0;

        // Include all the fields for the purchase update
        const purchaseData = {
          items: values.items.map((item) => {
            // Calculate item's proportion of the total for discount distribution
            const itemSubtotal = item.quantity * item.costAtPurchase;
            const itemProportion =
              subtotalBeforeDiscount > 0
                ? itemSubtotal / subtotalBeforeDiscount
                : 0;

            // Distribute global discount proportionally to items
            let itemDiscountPercentage = 0;
            let itemDiscountAmount = 0;

            if (globalDiscountAmount > 0) {
              if (values.globalDiscountPercentage > 0) {
                // If global discount is percentage-based, apply same percentage to all items
                itemDiscountPercentage = values.globalDiscountPercentage;
              } else {
                // If global discount is amount-based, distribute proportionally
                itemDiscountAmount = globalDiscountAmount * itemProportion;
              }
            }

            return {
              productId: item.productId,
              quantity: item.quantity,
              costAtPurchase: item.costAtPurchase,
              unit: item.unit || "Buah",
              tax: item.tax || "",
              // Include discount fields
              discountPercentage:
                itemDiscountPercentage > 0 ? itemDiscountPercentage : undefined,
              discountAmount:
                itemDiscountAmount > 0 ? itemDiscountAmount : undefined,
            };
          }),
          totalAmount: values.totalAmount,
          invoiceRef: autoInvoiceRef,
          supplierId: values.supplierId,
          isDraft: false, // Always set to false when publishing
          // New fields
          supplierEmail: values.supplierEmail || "",
          transactionDate: values.transactionDate || new Date(),
          paymentDueDate: values.paymentDueDate,
          transactionNumber: autoTransactionNumber,
          tags: values.tags || [],
          billingAddress: values.billingAddress || "",
          // Additional fields
          memo: values.memo || "",
          lampiran: values.lampiran || [],
          // Warehouse ID is stored in the database
          warehouseId: values.warehouseId || "",
        };

        const result = await updatePurchase(purchase.id, purchaseData);
        if (result.success) {
          toast.success(result.success);
          // Redirect after a short delay
          router.push(
            `/dashboard/purchases/detail/${autoTransactionNumber || purchase.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Save as draft to database
  const saveAsDraft = () => {
    startTransition(async () => {
      try {
        // Get current form values
        const values = form.getValues();

        // Generate transaction number and invoice reference if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        if (!autoTransactionNumber) {
          autoTransactionNumber = await getNextTransactionNumber(
            "TRX",
            purchase.createdAt
          );
        }

        if (!autoInvoiceRef) {
          autoInvoiceRef = await getNextTransactionNumber(
            "INV",
            purchase.createdAt
          );
        }

        // Calculate subtotal before discount for proportional distribution
        const subtotalBeforeDiscount = values.items.reduce((sum, item) => {
          return sum + item.quantity * item.costAtPurchase;
        }, 0);

        // Calculate global discount amount
        const globalDiscountAmount =
          values.globalDiscountPercentage > 0
            ? subtotalBeforeDiscount * (values.globalDiscountPercentage / 100)
            : values.globalDiscountAmount || 0;

        // Extract the fields for the purchase update (only fields in base PurchaseSchema)
        const purchaseData = {
          items: values.items.map((item) => {
            // Calculate item's proportion of the total for discount distribution
            const itemSubtotal = item.quantity * item.costAtPurchase;
            const itemProportion =
              subtotalBeforeDiscount > 0
                ? itemSubtotal / subtotalBeforeDiscount
                : 0;

            // Distribute global discount proportionally to items
            let itemDiscountPercentage = 0;
            let itemDiscountAmount = 0;

            if (globalDiscountAmount > 0) {
              if (values.globalDiscountPercentage > 0) {
                // If global discount is percentage-based, apply same percentage to all items
                itemDiscountPercentage = values.globalDiscountPercentage;
              } else {
                // If global discount is amount-based, distribute proportionally
                itemDiscountAmount = globalDiscountAmount * itemProportion;
              }
            }

            return {
              productId: item.productId,
              quantity: item.quantity,
              costAtPurchase: item.costAtPurchase,
              unit: item.unit || "Buah",
              tax: item.tax || "",
              // Include discount fields
              discountPercentage:
                itemDiscountPercentage > 0 ? itemDiscountPercentage : undefined,
              discountAmount:
                itemDiscountAmount > 0 ? itemDiscountAmount : undefined,
            };
          }),
          totalAmount: values.totalAmount,
          invoiceRef: autoInvoiceRef,
          supplierId: values.supplierId,
          isDraft: true, // Set to true when saving as draft
          // New fields
          supplierEmail: values.supplierEmail || "",
          transactionDate: values.transactionDate || new Date(),
          paymentDueDate: values.paymentDueDate,
          transactionNumber: autoTransactionNumber,
          tags: values.tags || [],
          billingAddress: values.billingAddress || "",
          // Additional fields
          memo: values.memo || "",
          lampiran: values.lampiran || [],
          // Warehouse ID is stored in the database
          warehouseId: values.warehouseId || "",
        };

        const result = await updatePurchase(purchase.id, purchaseData);
        if (result.success) {
          toast.success("Pembelian berhasil disimpan sebagai draft!");
          // Redirect after a short delay
          router.push(
            `/dashboard/purchases/detail/${autoTransactionNumber || purchase.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="w-full px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Edit Pembelian</h1>
          <p className="text-muted-foreground">
            Perbarui transaksi pembelian dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button variant="outline" asChild className="gap-2">
          <Link
            href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-6">
            {/* Main Form Content */}
            <CombinedPurchaseForm
              control={form.control}
              isPending={isPending}
              products={products}
              suppliers={suppliers}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
              createdAt={purchase.createdAt}
              setValue={form.setValue}
              trigger={form.trigger}
            />

            {/* Purchase Summary section removed */}
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              asChild
              disabled={isPending}
            >
              <Link
                href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
              >
                Batal
              </Link>
            </Button>
            <Button
              type="button"
              variant="secondary"
              disabled={isPending}
              className="gap-2 cursor-pointer hover:bg-primary/10"
              onClick={saveAsDraft}
            >
              <Save className="h-4 w-4" />
              <span>Simpan ke Draft</span>
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="gap-2 cursor-pointer"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span>Simpan Perubahan</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default EnhancedPurchaseEditPage;
