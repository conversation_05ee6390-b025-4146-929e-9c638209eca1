import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Save } from "lucide-react";

interface PurchaseFormActionsProps {
  isPending: boolean;
  onCancel: () => void; // Use router.back() or a custom handler
  onSaveAsDraft?: () => void; // Optional draft handler
}

const PurchaseFormActions: React.FC<PurchaseFormActionsProps> = ({
  isPending,
  onCancel,
  onSaveAsDraft,
}) => {
  return (
    <div className="flex justify-end space-x-2 pt-4">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        disabled={isPending}
      >
        Batal
      </Button>
      {onSaveAsDraft && (
        <Button
          type="button"
          variant="secondary"
          onClick={onSaveAsDraft}
          disabled={isPending}
          className="gap-2 cursor-pointer"
        >
          <Save className="h-4 w-4" />
          <span>Simpan ke Draft</span>
        </Button>
      )}
      <Button type="submit" disabled={isPending}>
        {isPending ? "Menyimpan..." : "Simpan Pembelian"}
      </Button>
    </div>
  );
};

export default PurchaseFormActions;
