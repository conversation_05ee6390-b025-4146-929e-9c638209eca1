import React, { useEffect, useState } from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
  Form,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  PurchaseFormValues,
  Supplier,
} from "@/components/pages/dashboard/purchases/new/types"; // Use alias for types
import { getNextTransactionNumber } from "@/actions/entities/purchases";
import {
  Calendar,
  Mail,
  Phone,
  Home,
  Hash,
  FileText,
  Building,
  X,
  Plus,
  Loader2,
  ChevronsUpDown,
  RefreshCw,
  Tags,
  WarehouseIcon,
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { SupplierModal } from "./SupplierModal";
import { Warehouse, WarehouseFormData } from "@/types/warehouse";
import { getWarehouses, createWarehouse } from "@/actions/entities/warehouses";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Warehouse form schema for the dialog
const warehouseSchema = z.object({
  name: z.string().min(1, "Nama gudang wajib diisi"),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  contactName: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

interface PurchaseInfoSectionProps {
  control: Control<PurchaseFormValues>;
  isPending: boolean;
  suppliers: Supplier[];
  createdAt?: string; // Optional createdAt for edit mode
  onSuppliersRefresh?: () => void; // Callback to refresh suppliers
  setValue?: (name: keyof PurchaseFormValues, value: any) => void; // For setting form values
  trigger?: (
    name?: keyof PurchaseFormValues | (keyof PurchaseFormValues)[]
  ) => Promise<boolean>; // Optional trigger function
}

const PurchaseInfoSection: React.FC<PurchaseInfoSectionProps> = ({
  control,
  isPending,
  suppliers,
  createdAt, // Add createdAt parameter
  onSuppliersRefresh,
  setValue,
  trigger,
}) => {
  // State for auto-generation settings
  const [autoTransactionNumber, setAutoTransactionNumber] =
    React.useState(false);
  const [autoInvoiceNumber, setAutoInvoiceNumber] = React.useState(false);
  const [tagInput, setTagInput] = React.useState("");

  // State for actual next transaction numbers
  const [nextTrxNumber, setNextTrxNumber] = React.useState<string>("");
  const [nextInvNumber, setNextInvNumber] = React.useState<string>("");
  const [isLoadingTrx, setIsLoadingTrx] = React.useState(false);
  const [isLoadingInv, setIsLoadingInv] = React.useState(false);

  // State for supplier dropdown
  const [supplierSearchTerm, setSupplierSearchTerm] = useState("");
  const [supplierDropdownOpen, setSupplierDropdownOpen] = useState(false);

  // State for supplier modal
  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);

  // State for warehouse dropdown
  const [warehouseSearchTerm, setWarehouseSearchTerm] = useState("");
  const [warehouseDropdownOpen, setWarehouseDropdownOpen] = useState(false);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [isLoadingWarehouses, setIsLoadingWarehouses] = useState(true);

  // State for warehouse creation dialog
  const [warehouseDialogOpen, setWarehouseDialogOpen] = useState(false);
  const [isCreatingWarehouse, setIsCreatingWarehouse] = useState(false);
  const warehouseFieldRef = React.useRef<((id: string) => void) | null>(null);

  // Warehouse form for dialog
  const warehouseForm = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: "",
      description: "",
      address: "",
      phone: "",
      email: "",
      contactName: "",
      isActive: true,
      isDefault: false,
    },
  });

  // Enhanced filtering - search across supplier name
  const filteredSuppliers = suppliers.filter((supplier) => {
    const searchLower = supplierSearchTerm.toLowerCase().trim();
    if (!searchLower) return true;

    return supplier.name.toLowerCase().includes(searchLower);
  });

  // Enhanced filtering - search across warehouse name
  const filteredWarehouses = warehouses.filter((warehouse) => {
    const searchLower = warehouseSearchTerm.toLowerCase().trim();
    if (!searchLower) return true;

    return warehouse.name.toLowerCase().includes(searchLower);
  });

  // Fetch warehouses on component mount
  useEffect(() => {
    const fetchWarehouses = async () => {
      try {
        setIsLoadingWarehouses(true);
        const warehouseData = await getWarehouses();
        setWarehouses(warehouseData);
      } catch (error) {
        console.error("Error fetching warehouses:", error);
      } finally {
        setIsLoadingWarehouses(false);
      }
    };

    fetchWarehouses();
  }, []);

  // Handle supplier creation
  const handleSupplierCreated = (newSupplier: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  }) => {
    // Refresh suppliers list if callback is provided
    if (onSuppliersRefresh) {
      onSuppliersRefresh();
    }

    // Auto-select the newly created supplier using setValue if available
    if (setValue) {
      setValue("supplierId", newSupplier.id);

      // Auto-populate supplier email if available
      if (newSupplier.email && newSupplier.email !== "-") {
        setValue("supplierEmail", newSupplier.email);
      } else {
        setValue("supplierEmail", "");
      }

      // Auto-populate supplier phone if available
      if (newSupplier.phone && newSupplier.phone !== "-") {
        setValue("supplierPhone", newSupplier.phone);
      } else {
        setValue("supplierPhone", "");
      }

      // Trigger validation to clear any error states
      if (trigger) {
        trigger(["supplierId", "supplierEmail", "supplierPhone"]);
      }
    }

    // Trigger form update
    setTimeout(() => {
      const event = new CustomEvent("supplierCreated", {
        detail: { supplierId: newSupplier.id },
      });
      window.dispatchEvent(event);
    }, 100);
  };

  // Handle warehouse form submission
  const handleWarehouseFormSubmit = warehouseForm.handleSubmit(
    async (data: WarehouseFormData) => {
      try {
        setIsCreatingWarehouse(true);
        const newWarehouse = await createWarehouse(data);

        // Refresh warehouses list
        const updatedWarehouses = await getWarehouses();
        setWarehouses(updatedWarehouses);

        // Auto-select the newly created warehouse
        if (setValue) {
          setValue("warehouseId", newWarehouse.id);
        }

        // Also use the field onChange directly
        if (warehouseFieldRef.current) {
          warehouseFieldRef.current(newWarehouse.id);
        }

        // Close dialog and reset form
        setWarehouseDialogOpen(false);
        warehouseForm.reset();
      } catch (error) {
        console.error("Error creating warehouse:", error);
      } finally {
        setIsCreatingWarehouse(false);
      }
    }
  );

  // Fetch next transaction number when auto-generation is toggled
  useEffect(() => {
    if (autoTransactionNumber && !nextTrxNumber) {
      setIsLoadingTrx(true);
      // Use createdAt date if available (edit mode), otherwise use current date
      getNextTransactionNumber("TRX", createdAt)
        .then((number) => {
          setNextTrxNumber(number);
        })
        .catch((error) => {
          console.error("Error fetching next transaction number:", error);
        })
        .finally(() => {
          setIsLoadingTrx(false);
        });
    }
  }, [autoTransactionNumber, nextTrxNumber, createdAt]);

  // Fetch next invoice number when auto-generation is toggled
  useEffect(() => {
    if (autoInvoiceNumber && !nextInvNumber) {
      setIsLoadingInv(true);
      // Use createdAt date if available (edit mode), otherwise use current date
      getNextTransactionNumber("INV", createdAt)
        .then((number) => {
          setNextInvNumber(number);
        })
        .catch((error) => {
          console.error("Error fetching next invoice number:", error);
        })
        .finally(() => {
          setIsLoadingInv(false);
        });
    }
  }, [autoInvoiceNumber, nextInvNumber, createdAt]);

  // Generate fallback example transaction numbers for placeholders
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const yearSuffix = String(year).slice(-2); // Get last 2 digits of year
  const fallbackTrxNumber = `BELI-${yearSuffix}B000001`;
  const fallbackInvNumber = `INV-${yearSuffix}B000001`;
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Supplier Selection */}
        <FormField
          control={control}
          name="supplierId"
          render={({ field }) => (
            <FormItem className="col-span-1">
              <FormLabel className="flex items-center gap-1.5">
                <Building className="h-4 w-4 text-indigo-600" />
                Supplier <span className="text-red-500 font-bold">*</span>
              </FormLabel>
              <div className="flex gap-2">
                <Popover
                  open={supplierDropdownOpen}
                  onOpenChange={setSupplierDropdownOpen}
                >
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={supplierDropdownOpen}
                        className={`flex-1 justify-between hover:bg-accent hover:text-accent-foreground cursor-pointer ${
                          !field.value ? "text-muted-foreground" : ""
                        }`}
                        disabled={isPending}
                      >
                        {suppliers.length === 0 ? (
                          <div className="flex items-center">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Memuat supplier...
                          </div>
                        ) : field.value ? (
                          <div className="flex items-center">
                            <Building className="mr-2 h-4 w-4" />
                            <span>
                              {suppliers.find(
                                (supplier) => supplier.id === field.value
                              )?.name || "Pilih supplier"}
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Building className="mr-2 h-4 w-4" />
                            <span>Pilih supplier</span>
                          </div>
                        )}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                    <Command shouldFilter={false}>
                      <CommandInput
                        placeholder="Cari supplier..."
                        value={supplierSearchTerm}
                        onValueChange={setSupplierSearchTerm}
                        className="h-9"
                        onFocus={() => {
                          if (!supplierDropdownOpen) {
                            setSupplierDropdownOpen(true);
                          }
                        }}
                      />
                      <CommandList className="max-h-[300px] overflow-y-auto">
                        <CommandGroup>
                          {/* Show all filtered suppliers or just 3 if not searching */}
                          {(supplierSearchTerm
                            ? filteredSuppliers
                            : suppliers.slice(0, 3)
                          ).map((supplier) => (
                            <div key={supplier.id} className="px-2">
                              <button
                                type="button"
                                className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                                onClick={() => {
                                  field.onChange(supplier.id);
                                  setSupplierDropdownOpen(false);
                                  setSupplierSearchTerm("");

                                  // Auto-populate supplier email and phone using setValue if available
                                  if (setValue) {
                                    // Set supplier email if available
                                    if (
                                      supplier.email &&
                                      supplier.email !== "-"
                                    ) {
                                      setValue("supplierEmail", supplier.email);
                                    } else {
                                      setValue("supplierEmail", "");
                                    }

                                    // Set supplier phone if available
                                    if (
                                      supplier.phone &&
                                      supplier.phone !== "-"
                                    ) {
                                      setValue("supplierPhone", supplier.phone);
                                    } else {
                                      setValue("supplierPhone", "");
                                    }

                                    // Trigger validation to clear any error states
                                    if (trigger) {
                                      trigger([
                                        "supplierEmail",
                                        "supplierPhone",
                                      ]);
                                    }
                                  }
                                }}
                              >
                                <Building className="mr-2 h-4 w-4 shrink-0 text-indigo-500" />
                                <div className="flex flex-col flex-grow">
                                  <span className="font-medium">
                                    {supplier.name}
                                  </span>
                                  {supplier.email && supplier.email !== "-" && (
                                    <span className="text-xs text-muted-foreground">
                                      {supplier.email}
                                    </span>
                                  )}
                                </div>
                                <span className="text-xs text-indigo-500 ml-2">
                                  Pilih
                                </span>
                              </button>
                            </div>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>

                {/* Add Supplier Button */}
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="shrink-0 cursor-pointer"
                  onClick={() => setIsSupplierModalOpen(true)}
                  disabled={isPending}
                  title="Tambah supplier baru"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Supplier Email and Phone */}
        <div className="col-span-1 grid grid-cols-1 gap-2 md:grid-cols-2">
          <FormField
            control={control}
            name="supplierEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Mail className="h-4 w-4 text-blue-600" />
                  Email Supplier
                  <span className="text-red-500 font-bold">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="supplierPhone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Phone className="h-4 w-4 text-green-600" />
                  Nomor Handphone
                </FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder="08123456789"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Transaction Date */}
        <FormField
          control={control}
          name="transactionDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-green-600" />
                Tgl. Transaksi
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : new Date()}
                  setDate={(date) => onChange(date || new Date())}
                  placeholder="Pilih tanggal transaksi"
                  disabled={isPending}
                  className="w-full cursor-pointer"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Payment Due Date */}
        <FormField
          control={control}
          name="paymentDueDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-red-600" />
                Tgl. Jatuh Tempo
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : undefined}
                  setDate={onChange}
                  placeholder="Pilih tanggal jatuh tempo"
                  disabled={isPending}
                  className="w-full cursor-pointer"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transaction Number */}
        <FormField
          control={control}
          name="transactionNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Hash className="h-4 w-4 text-purple-600" />
                Nomor Transaksi
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoTransactionNumber
                        ? isLoadingTrx
                          ? "Loading..."
                          : `Auto - ${nextTrxNumber || fallbackTrxNumber}`
                        : `Contoh: BELI-${yearSuffix}B000001`
                    }
                    {...field}
                    disabled={isPending || autoTransactionNumber}
                    value={autoTransactionNumber ? "" : field.value}
                    onChange={(e) => {
                      if (!autoTransactionNumber) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingTrx && autoTransactionNumber ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 cursor-pointer"
                    onClick={() => {
                      setAutoTransactionNumber(!autoTransactionNumber);
                      if (!autoTransactionNumber) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoTransactionNumber
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <RefreshCw className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoTransactionNumber && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor transaksi akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Invoice Reference */}
        <FormField
          control={control}
          name="invoiceRef"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <FileText className="h-4 w-4 text-yellow-600" />
                Nomor Invoice
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoInvoiceNumber
                        ? isLoadingInv
                          ? "Loading..."
                          : `Auto - ${nextInvNumber || fallbackInvNumber}`
                        : `Contoh: INV-${yearSuffix}B000001`
                    }
                    {...field}
                    disabled={isPending || autoInvoiceNumber}
                    value={autoInvoiceNumber ? "" : field.value}
                    onChange={(e) => {
                      if (!autoInvoiceNumber) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingInv && autoInvoiceNumber ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 cursor-pointer"
                    onClick={() => {
                      setAutoInvoiceNumber(!autoInvoiceNumber);
                      if (!autoInvoiceNumber) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoInvoiceNumber
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <RefreshCw className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoInvoiceNumber && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor invoice akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Warehouse */}
        <FormField
          control={control}
          name="warehouseId"
          render={({ field }) => {
            // Store the field onChange for warehouse creation
            warehouseFieldRef.current = field.onChange;

            return (
              <FormItem className="col-span-1">
                <FormLabel className="flex items-center gap-1.5">
                  <WarehouseIcon className="h-4 w-4 text-violet-600" />
                  Gudang
                </FormLabel>
                <div className="flex gap-2">
                  <Popover
                    open={warehouseDropdownOpen}
                    onOpenChange={setWarehouseDropdownOpen}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={warehouseDropdownOpen}
                          className={`flex-1 justify-between hover:bg-accent hover:text-accent-foreground cursor-pointer ${
                            !field.value ? "text-muted-foreground" : ""
                          }`}
                          disabled={isPending}
                        >
                          {isLoadingWarehouses ? (
                            <div className="flex items-center">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Memuat gudang...
                            </div>
                          ) : field.value ? (
                            <div className="flex items-center">
                              <WarehouseIcon className="mr-2 h-4 w-4" />
                              <span>
                                {warehouses.find(
                                  (warehouse) => warehouse.id === field.value
                                )?.name || "Pilih gudang"}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <WarehouseIcon className="mr-2 h-4 w-4" />
                              <span>Pilih gudang</span>
                            </div>
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                      <Command shouldFilter={false}>
                        <CommandInput
                          placeholder="Cari gudang..."
                          value={warehouseSearchTerm}
                          onValueChange={setWarehouseSearchTerm}
                          className="h-9"
                        />
                        <CommandList className="max-h-[300px] overflow-y-auto">
                          <CommandGroup>
                            {/* Show all filtered warehouses or just 3 if not searching */}
                            {(warehouseSearchTerm
                              ? filteredWarehouses.filter((w) => w.isActive)
                              : warehouses.filter((w) => w.isActive).slice(0, 3)
                            ).map((warehouse) => (
                              <div key={warehouse.id} className="px-2">
                                <button
                                  type="button"
                                  className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                                  onClick={() => {
                                    field.onChange(warehouse.id);
                                    setWarehouseDropdownOpen(false);
                                    setWarehouseSearchTerm("");
                                  }}
                                >
                                  <WarehouseIcon className="mr-2 h-4 w-4 shrink-0 text-violet-500" />
                                  <div className="flex flex-col flex-grow">
                                    <span className="font-medium">
                                      {warehouse.name}
                                    </span>
                                    {warehouse.description && (
                                      <span className="text-xs text-muted-foreground">
                                        {warehouse.description}
                                      </span>
                                    )}
                                  </div>
                                  <span className="text-xs text-violet-500 ml-2">
                                    Pilih
                                  </span>
                                </button>
                              </div>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  {/* Add Warehouse Button */}
                  <Dialog
                    open={warehouseDialogOpen}
                    onOpenChange={setWarehouseDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="shrink-0 cursor-pointer"
                        type="button"
                        disabled={isPending}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Tambah Gudang Baru</DialogTitle>
                        <DialogDescription>
                          Buat gudang baru untuk transaksi ini.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...warehouseForm}>
                        <form
                          onSubmit={handleWarehouseFormSubmit}
                          className="space-y-4"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Warehouse Name */}
                            <FormField
                              control={warehouseForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem className="col-span-2">
                                  <FormLabel>
                                    Nama Gudang{" "}
                                    <span className="text-red-500">*</span>
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Masukkan nama gudang"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Description */}
                            <FormField
                              control={warehouseForm.control}
                              name="description"
                              render={({ field }) => (
                                <FormItem className="col-span-2">
                                  <FormLabel>Deskripsi</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Deskripsi gudang (opsional)"
                                      rows={3}
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Address */}
                            <FormField
                              control={warehouseForm.control}
                              name="address"
                              render={({ field }) => (
                                <FormItem className="col-span-2">
                                  <FormLabel>Alamat</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Alamat gudang (opsional)"
                                      rows={2}
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Contact Name */}
                            <FormField
                              control={warehouseForm.control}
                              name="contactName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nama Kontak</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nama kontak (opsional)"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Phone */}
                            <FormField
                              control={warehouseForm.control}
                              name="phone"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Telepon</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nomor telepon (opsional)"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Email */}
                            <FormField
                              control={warehouseForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem className="col-span-2">
                                  <FormLabel>Email</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="email"
                                      placeholder="Email gudang (opsional)"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="flex justify-end space-x-2 pt-4">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setWarehouseDialogOpen(false)}
                              disabled={isCreatingWarehouse}
                            >
                              Batal
                            </Button>
                            <Button
                              type="submit"
                              disabled={isCreatingWarehouse}
                              className="cursor-pointer"
                            >
                              {isCreatingWarehouse ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Menyimpan...
                                </>
                              ) : (
                                "Simpan Gudang"
                              )}
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        {/* Tags */}
        <FormField
          control={control}
          name="tags"
          render={({ field }) => {
            const handleAddTag = () => {
              if (tagInput.trim()) {
                // Check if we already have 3 tags
                if ((field.value || []).length >= 3) {
                  return; // Don't add more tags
                }

                // Check if the tag already exists
                if (!field.value?.includes(tagInput.trim())) {
                  const newTags = [...(field.value || []), tagInput.trim()];
                  field.onChange(newTags);
                }
                setTagInput("");
              }
            };

            const handleKeyDown = (e: React.KeyboardEvent) => {
              if (e.key === "Enter" || e.key === ",") {
                e.preventDefault();

                // Check if we already have 3 tags
                if ((field.value || []).length >= 3) {
                  return; // Don't add more tags
                }

                // If comma is pressed, we might have multiple tags
                if (e.key === ",") {
                  const tags = tagInput
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag !== "");

                  if (tags.length > 0) {
                    // Add all tags that don't already exist, up to the limit
                    const existingTags = field.value || [];
                    const newTags = [...existingTags];

                    tags.forEach((tag) => {
                      if (!existingTags.includes(tag) && newTags.length < 3) {
                        newTags.push(tag);
                      }
                    });

                    field.onChange(newTags);
                    setTagInput("");
                    return;
                  }
                }

                handleAddTag();
              }
            };

            return (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Tags className="h-4 w-4 text-teal-600" />
                  Tag Pembelian
                  <span className="text-xs text-muted-foreground ml-2">
                    ({(field.value || []).length}/3)
                  </span>
                </FormLabel>

                {/* Input for new tags - Moved to top */}
                <div className="flex gap-2 mb-3">
                  <FormControl>
                    <Input
                      placeholder={
                        (field.value || []).length >= 3
                          ? "Maksimal 3 tag telah tercapai"
                          : "Tambahkan tag (tekan Enter atau koma)"
                      }
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      disabled={isPending || (field.value || []).length >= 3}
                      className="flex-1"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={handleAddTag}
                    disabled={
                      isPending ||
                      !tagInput.trim() ||
                      (field.value || []).length >= 3
                    }
                    className="border-teal-200 hover:bg-teal-100 hover:text-teal-800 cursor-pointer"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Tambah
                  </Button>
                </div>

                {/* Show warning when limit is reached */}
                {(field.value || []).length >= 3 && (
                  <p className="text-xs text-amber-600 mb-2">
                    Maksimal 3 tag telah tercapai. Hapus tag yang ada untuk
                    menambah yang baru.
                  </p>
                )}

                {/* Display existing tags - Moved to bottom */}
                <div className="flex flex-wrap gap-2">
                  {field.value && field.value.length > 0 ? (
                    field.value.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 flex items-center gap-1 shadow-sm"
                      >
                        {tag}
                        <button
                          type="button"
                          className="ml-1 hover:text-red-500 cursor-pointer"
                          onClick={() => {
                            const newTags = [...(field.value || [])];
                            newTags.splice(index, 1);
                            field.onChange(newTags);
                          }}
                          disabled={isPending}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      Tag untuk memudahkan pencarian dan pengelompokan
                    </span>
                  )}
                </div>

                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </div>

      {/* Billing Address */}
      <FormField
        control={control}
        name="billingAddress"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-1.5">
              <Home className="h-4 w-4 text-indigo-600" />
              Alamat Supplier
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Masukkan alamat lengkap untuk penagihan"
                className="resize-y min-h-[80px]"
                {...field}
                disabled={isPending}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Supplier Modal */}
      <SupplierModal
        isOpen={isSupplierModalOpen}
        onClose={() => setIsSupplierModalOpen(false)}
        onSupplierCreated={handleSupplierCreated}
      />
    </div>
  );
};

export default PurchaseInfoSection;
