"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON><PERSON>3, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp,
  Calendar,
  Filter,
  Download,
  Maximize2
} from "lucide-react";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  Composed<PERSON><PERSON>,
  <PERSON>att<PERSON>,
  <PERSON>atter<PERSON>hart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts";
import { getSalesReportData, getPurchaseReportData, getProductReportData } from "@/actions/reports/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface DataVisualizationProps {
  filters: FilterState;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

const CHART_COLORS = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#EC4899", "#06B6D4", "#84CC16"];

export const DataVisualization: React.FC<DataVisualizationProps> = ({ filters }) => {
  const [loading, setLoading] = useState(true);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [purchaseData, setPurchaseData] = useState<any[]>([]);
  const [productData, setProductData] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [salesResult, purchaseResult, productResult] = await Promise.all([
          getSalesReportData(filters.dateRange),
          getPurchaseReportData(filters.dateRange),
          getProductReportData(filters.dateRange)
        ]);

        if (salesResult.success && salesResult.data) {
          setSalesData(salesResult.data);
        }

        if (purchaseResult.success && purchaseResult.data) {
          setPurchaseData(purchaseResult.data);
        }

        if (productResult.success && productResult.data) {
          setProductData(productResult.data);
        }

        // Prepare various chart data
        prepareChartData(salesResult.data || [], purchaseResult.data || [], productResult.data || []);
      } catch (error) {
        console.error("Error fetching visualization data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]);

  const prepareChartData = (sales: any[], purchases: any[], products: any[]) => {
    // Monthly comparison data
    const monthlyData = [];
    for (let i = 0; i < 12; i++) {
      const month = new Date();
      month.setMonth(month.getMonth() - i);
      const monthName = month.toLocaleDateString('id-ID', { month: 'short' });
      
      monthlyData.unshift({
        month: monthName,
        sales: Math.random() * 10000000 + 1000000,
        purchases: Math.random() * 8000000 + 500000,
        profit: Math.random() * 3000000 + 200000
      });
    }

    // Category distribution
    const categoryData = products.reduce((acc: any, product: any) => {
      const category = product.category || 'Lainnya';
      if (!acc[category]) {
        acc[category] = { name: category, value: 0, count: 0 };
      }
      acc[category].value += product.revenue || 0;
      acc[category].count += 1;
      return acc;
    }, {});

    const categoryPieData = Object.values(categoryData).map((item: any, index) => ({
      ...item,
      color: CHART_COLORS[index % CHART_COLORS.length]
    }));

    // Top products
    const topProducts = products
      .sort((a, b) => (b.revenue || 0) - (a.revenue || 0))
      .slice(0, 10)
      .map(product => ({
        name: product.name.length > 15 ? product.name.substring(0, 15) + '...' : product.name,
        revenue: product.revenue || 0,
        sold: product.sold || 0,
        stock: product.stock || 0
      }));

    // Daily sales trend (last 30 days)
    const dailyTrend = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dailyTrend.push({
        date: date.toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit' }),
        sales: Math.random() * 2000000 + 100000,
        transactions: Math.floor(Math.random() * 50 + 5)
      });
    }

    // Performance radar data
    const performanceData = [
      { subject: 'Penjualan', A: Math.random() * 100, fullMark: 100 },
      { subject: 'Keuntungan', A: Math.random() * 100, fullMark: 100 },
      { subject: 'Pelanggan', A: Math.random() * 100, fullMark: 100 },
      { subject: 'Produk', A: Math.random() * 100, fullMark: 100 },
      { subject: 'Efisiensi', A: Math.random() * 100, fullMark: 100 },
      { subject: 'Pertumbuhan', A: Math.random() * 100, fullMark: 100 }
    ];

    // Scatter plot data (Sales vs Profit)
    const scatterData = products.slice(0, 20).map(product => ({
      x: product.revenue || 0,
      y: product.profit || 0,
      name: product.name
    }));

    setChartData({
      monthly: monthlyData,
      categoryPie: categoryPieData,
      topProducts,
      dailyTrend,
      performance: performanceData,
      scatter: scatterData
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-slate-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Chart Type Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            Tren
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Distribusi
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Performa
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Maximize2 className="h-4 w-4" />
            Advanced
          </TabsTrigger>
        </TabsList>

        {/* Overview Charts */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Perbandingan Bulanan</CardTitle>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <ComposedChart data={chartData.monthly}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => formatCurrency(value)} />
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                        <Legend />
                        <Bar dataKey="sales" fill="#10B981" name="Penjualan" />
                        <Bar dataKey="purchases" fill="#EF4444" name="Pembelian" />
                        <Line type="monotone" dataKey="profit" stroke="#3B82F6" strokeWidth={3} name="Keuntungan" />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Produk Terlaris</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData.topProducts} layout="horizontal">
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" tickFormatter={(value) => formatCurrency(value)} />
                        <YAxis dataKey="name" type="category" width={100} />
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                        <Bar dataKey="revenue" fill="#8B5CF6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Trends Charts */}
        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Tren Penjualan Harian</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={chartData.dailyTrend}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis tickFormatter={(value) => formatCurrency(value)} />
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                        <Area 
                          type="monotone" 
                          dataKey="sales" 
                          stroke="#10B981" 
                          fill="#10B981" 
                          fillOpacity={0.6}
                          name="Penjualan"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Jumlah Transaksi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsLineChart data={chartData.dailyTrend}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line 
                          type="monotone" 
                          dataKey="transactions" 
                          stroke="#3B82F6" 
                          strokeWidth={3}
                          name="Transaksi"
                        />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Distribution Charts */}
        <TabsContent value="distribution" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Kategori Produk</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={chartData.categoryPie}
                          cx="50%"
                          cy="50%"
                          outerRadius={100}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {chartData.categoryPie?.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                        <Legend />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Jumlah Produk per Kategori</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData.categoryPie}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="count" fill="#F59E0B" name="Jumlah Produk" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Performance Charts */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Radar Performa Bisnis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={chartData.performance}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="subject" />
                        <PolarRadiusAxis angle={90} domain={[0, 100]} />
                        <Radar
                          name="Performa"
                          dataKey="A"
                          stroke="#3B82F6"
                          fill="#3B82F6"
                          fillOpacity={0.3}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Analisis Penjualan vs Keuntungan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <ScatterChart data={chartData.scatter}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="x" 
                          name="Penjualan"
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <YAxis 
                          dataKey="y" 
                          name="Keuntungan"
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <Tooltip 
                          formatter={(value, name) => [
                            formatCurrency(value as number),
                            name === 'x' ? 'Penjualan' : 'Keuntungan'
                          ]}
                        />
                        <Scatter dataKey="y" fill="#8B5CF6" />
                      </ScatterChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Advanced Charts */}
        <TabsContent value="advanced" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <Maximize2 className="h-16 w-16 mx-auto text-slate-400 mb-4" />
            <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
              Visualisasi Advanced
            </h3>
            <p className="text-slate-500 dark:text-slate-400 mb-6">
              Fitur visualisasi advanced seperti heatmap, treemap, dan chart interaktif akan segera hadir
            </p>
            <Button variant="outline">
              Request Fitur Baru
            </Button>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
