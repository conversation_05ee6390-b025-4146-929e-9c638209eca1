"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Activity, 
  Clock, 
  Users, 
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Zap,
  Eye,
  RefreshCw,
  Wifi,
  WifiOff
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from "recharts";
import { getSalesReportData } from "@/actions/reports/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface RealtimeStatsProps {
  filters: FilterState;
  refreshInterval: number | null;
}

interface RealtimeMetric {
  id: string;
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
  color: string;
  status: 'up' | 'down' | 'stable';
}

interface ActivityLog {
  id: string;
  type: 'sale' | 'purchase' | 'user' | 'system';
  message: string;
  timestamp: Date;
  amount?: number;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

export const RealtimeStats: React.FC<RealtimeStatsProps> = ({ filters, refreshInterval }) => {
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [metrics, setMetrics] = useState<RealtimeMetric[]>([]);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [realtimeData, setRealtimeData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Simulate real-time data updates
  useEffect(() => {
    const generateRealtimeMetrics = () => {
      const now = new Date();
      const baseMetrics: RealtimeMetric[] = [
        {
          id: 'active-users',
          title: 'Pengguna Aktif',
          value: Math.floor(Math.random() * 50 + 10).toString(),
          change: Math.random() * 20 - 10,
          icon: <Users className="h-5 w-5 text-white" />,
          color: 'bg-blue-500',
          status: Math.random() > 0.5 ? 'up' : 'down'
        },
        {
          id: 'sales-today',
          title: 'Penjualan Hari Ini',
          value: formatCurrency(Math.random() * 10000000 + 1000000),
          change: Math.random() * 30 - 15,
          icon: <ShoppingCart className="h-5 w-5 text-white" />,
          color: 'bg-green-500',
          status: Math.random() > 0.3 ? 'up' : 'down'
        },
        {
          id: 'transactions',
          title: 'Transaksi/Jam',
          value: Math.floor(Math.random() * 100 + 20).toString(),
          change: Math.random() * 25 - 12.5,
          icon: <Activity className="h-5 w-5 text-white" />,
          color: 'bg-purple-500',
          status: Math.random() > 0.4 ? 'up' : 'stable'
        },
        {
          id: 'conversion',
          title: 'Tingkat Konversi',
          value: `${(Math.random() * 10 + 5).toFixed(1)}%`,
          change: Math.random() * 15 - 7.5,
          icon: <TrendingUp className="h-5 w-5 text-white" />,
          color: 'bg-amber-500',
          status: Math.random() > 0.6 ? 'up' : 'down'
        },
        {
          id: 'page-views',
          title: 'Tampilan Halaman',
          value: Math.floor(Math.random() * 1000 + 500).toString(),
          change: Math.random() * 20 - 10,
          icon: <Eye className="h-5 w-5 text-white" />,
          color: 'bg-indigo-500',
          status: Math.random() > 0.5 ? 'up' : 'stable'
        }
      ];

      setMetrics(baseMetrics);
      setLastUpdate(now);
    };

    const generateActivityLogs = () => {
      const activities: ActivityLog[] = [];
      const types: ActivityLog['type'][] = ['sale', 'purchase', 'user', 'system'];
      const messages = {
        sale: ['Penjualan baru', 'Pembayaran diterima', 'Pesanan selesai'],
        purchase: ['Pembelian baru', 'Stok ditambah', 'Invoice diterima'],
        user: ['Pengguna baru terdaftar', 'Login berhasil', 'Profil diperbarui'],
        system: ['Backup selesai', 'Update sistem', 'Maintenance selesai']
      };

      for (let i = 0; i < 10; i++) {
        const type = types[Math.floor(Math.random() * types.length)];
        const message = messages[type][Math.floor(Math.random() * messages[type].length)];
        
        activities.push({
          id: `activity-${i}`,
          type,
          message,
          timestamp: new Date(Date.now() - Math.random() * 3600000), // Last hour
          amount: type === 'sale' ? Math.random() * 1000000 + 50000 : undefined
        });
      }

      setActivityLogs(activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()));
    };

    const generateRealtimeChartData = () => {
      const data = [];
      const now = new Date();
      
      for (let i = 29; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000); // Last 30 minutes
        data.push({
          time: time.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' }),
          sales: Math.random() * 1000000 + 100000,
          users: Math.random() * 50 + 10,
          transactions: Math.random() * 20 + 5
        });
      }
      
      setRealtimeData(data);
    };

    // Initial load
    generateRealtimeMetrics();
    generateActivityLogs();
    generateRealtimeChartData();
    setLoading(false);

    // Set up refresh interval
    let interval: NodeJS.Timeout;
    if (refreshInterval) {
      interval = setInterval(() => {
        generateRealtimeMetrics();
        generateActivityLogs();
        generateRealtimeChartData();
        
        // Simulate occasional connection issues
        if (Math.random() < 0.05) {
          setIsConnected(false);
          setTimeout(() => setIsConnected(true), 2000);
        }
      }, refreshInterval * 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [refreshInterval]);

  const getActivityIcon = (type: ActivityLog['type']) => {
    switch (type) {
      case 'sale':
        return <ShoppingCart className="h-4 w-4 text-green-500" />;
      case 'purchase':
        return <TrendingDown className="h-4 w-4 text-blue-500" />;
      case 'user':
        return <Users className="h-4 w-4 text-purple-500" />;
      case 'system':
        return <Zap className="h-4 w-4 text-amber-500" />;
      default:
        return <Activity className="h-4 w-4 text-slate-500" />;
    }
  };

  const getActivityColor = (type: ActivityLog['type']) => {
    switch (type) {
      case 'sale':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'purchase':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'user':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'system':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200';
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-slate-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
            isConnected 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          }`}>
            {isConnected ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
            {isConnected ? 'Terhubung' : 'Terputus'}
          </div>
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <Clock className="h-4 w-4" />
            Update terakhir: {lastUpdate.toLocaleTimeString('id-ID')}
          </div>
        </div>
        
        {refreshInterval && (
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Refresh setiap {refreshInterval}s
          </div>
        )}
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
                  {metric.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${metric.color}`}>
                  {metric.icon}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                  {metric.value}
                </div>
                <div className="flex items-center text-sm">
                  {metric.status === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : metric.status === 'down' ? (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  ) : (
                    <Activity className="h-4 w-4 text-slate-500 mr-1" />
                  )}
                  <span className={
                    metric.status === 'up' ? "text-green-600" : 
                    metric.status === 'down' ? "text-red-600" : "text-slate-600"
                  }>
                    {Math.abs(metric.change).toFixed(1)}%
                  </span>
                  <span className="text-slate-500 ml-1">dari sebelumnya</span>
                </div>
              </CardContent>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${metric.color}`} />
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Real-time Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Aktivitas Real-time (30 Menit Terakhir)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={realtimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'sales' ? formatCurrency(value as number) : value,
                        name === 'sales' ? 'Penjualan' : 
                        name === 'users' ? 'Pengguna' : 'Transaksi'
                      ]}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="sales" 
                      stroke="#10B981" 
                      fill="#10B981" 
                      fillOpacity={0.3}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="users" 
                      stroke="#3B82F6" 
                      fill="#3B82F6" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Activity Log */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Log Aktivitas Terbaru
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-[300px] overflow-y-auto">
                {activityLogs.map((activity) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-slate-50 dark:bg-slate-800"
                  >
                    <div className="flex-shrink-0">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                          {activity.message}
                        </p>
                        <Badge className={getActivityColor(activity.type)}>
                          {activity.type}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-xs text-slate-500">
                          {activity.timestamp.toLocaleTimeString('id-ID')}
                        </p>
                        {activity.amount && (
                          <p className="text-xs font-medium text-green-600">
                            {formatCurrency(activity.amount)}
                          </p>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};
