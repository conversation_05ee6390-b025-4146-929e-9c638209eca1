"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FunnelIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";

export interface SalesFilterState {
  customer?: string;
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  amountRange?: {
    min?: number;
    max?: number;
  };
  status?: "all" | "draft" | "completed";
  hasInvoice?: boolean;
  warehouse?: string;
  isWholesale?: boolean;
}

interface SalesFilterProps {
  filters: SalesFilterState;
  onFilterChange: (filters: SalesFilterState) => void;
  customers?: Array<{ id: string; name: string }>;
  warehouses?: Array<{ id: string; name: string }>;
}

export const SalesFilter: React.FC<SalesFilterProps> = ({
  filters,
  onFilterChange,
  customers = [],
  warehouses = [],
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<SalesFilterState>(filters);

  // Apply filters
  const applyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };

  // Reset filters
  const resetFilters = () => {
    const emptyFilters: SalesFilterState = {
      customer: undefined,
      dateRange: { start: undefined, end: undefined },
      amountRange: { min: undefined, max: undefined },
      status: "all",
      hasInvoice: undefined,
      warehouse: undefined,
      isWholesale: undefined,
    };
    setLocalFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.customer) count++;
    if (filters.dateRange?.start || filters.dateRange?.end) count++;
    if (filters.amountRange?.min || filters.amountRange?.max) count++;
    if (filters.status && filters.status !== "all") count++;
    if (filters.hasInvoice !== undefined) count++;
    if (filters.warehouse) count++;
    if (filters.isWholesale !== undefined) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer relative"
        >
          <FunnelIcon className="mr-2 h-5 w-5" />
          Filter
          {activeFilterCount > 0 && (
            <Badge
              variant="destructive"
              className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <AdjustmentsHorizontalIcon className="h-5 w-5" />
            Filter Penjualan
          </DialogTitle>
          <DialogDescription>
            Gunakan filter untuk mempersempit pencarian data penjualan
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 p-2">
          {/* Customer Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Pelanggan</Label>
            <Select
              value={localFilters.customer || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  customer: value === "all" ? undefined : value,
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih pelanggan" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Pelanggan</SelectItem>
                {customers.map((customer) => (
                  <SelectItem key={customer.id} value={customer.id}>
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Date Range Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Rentang Tanggal</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs text-gray-500">Tanggal Mulai</Label>
                <Input
                  type="date"
                  value={
                    localFilters.dateRange?.start
                      ?.toISOString()
                      .split("T")[0] || ""
                  }
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      dateRange: {
                        ...prev.dateRange,
                        start: e.target.value
                          ? new Date(e.target.value)
                          : undefined,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <Label className="text-xs text-gray-500">Tanggal Akhir</Label>
                <Input
                  type="date"
                  value={
                    localFilters.dateRange?.end?.toISOString().split("T")[0] ||
                    ""
                  }
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      dateRange: {
                        ...prev.dateRange,
                        end: e.target.value
                          ? new Date(e.target.value)
                          : undefined,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Amount Range Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Rentang Total Penjualan
            </Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs text-gray-500">Minimum (Rp)</Label>
                <Input
                  type="number"
                  placeholder="0"
                  value={localFilters.amountRange?.min || ""}
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      amountRange: {
                        ...prev.amountRange,
                        min: e.target.value
                          ? Number(e.target.value)
                          : undefined,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <Label className="text-xs text-gray-500">Maksimum (Rp)</Label>
                <Input
                  type="number"
                  placeholder="Tidak terbatas"
                  value={localFilters.amountRange?.max || ""}
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      amountRange: {
                        ...prev.amountRange,
                        max: e.target.value
                          ? Number(e.target.value)
                          : undefined,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Status Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Status Transaksi</Label>
            <Select
              value={localFilters.status || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  status: value as any,
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="completed">Selesai</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Warehouse Filter */}
          {warehouses.length > 0 && (
            <>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Gudang</Label>
                <Select
                  value={localFilters.warehouse || "all"}
                  onValueChange={(value) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      warehouse: value === "all" ? undefined : value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih gudang" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Gudang</SelectItem>
                    {warehouses.map((warehouse) => (
                      <SelectItem key={warehouse.id} value={warehouse.id}>
                        {warehouse.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Separator />
            </>
          )}

          {/* Additional Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Opsi Lainnya</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasInvoice"
                  checked={localFilters.hasInvoice === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      hasInvoice: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="hasInvoice" className="text-sm cursor-pointer">
                  Memiliki nomor faktur
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isWholesale"
                  checked={localFilters.isWholesale === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      isWholesale: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="isWholesale" className="text-sm cursor-pointer">
                  Hanya penjualan grosir
                </Label>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons - Fixed at bottom */}
        <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
          <Button variant="outline" onClick={resetFilters}>
            Reset Filter
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Batal
            </Button>
            <Button onClick={applyFilters}>Terapkan Filter</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
