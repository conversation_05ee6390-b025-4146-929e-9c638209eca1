"use client";

import React, { useState } from "react";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { WarehouseManagement } from "../../warehouses/components/WarehouseManagement";
import { WarehouseStockOverview } from "../../warehouses/components/WarehouseStockOverview";
import { StockMovementHistory } from "../../warehouses/components/StockMovementHistory";

export const WarehouseTabContent: React.FC = () => {
  const [activeTab, setActiveTab] = useState("warehouses");

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="warehouses">Daftar Gudang</TabsTrigger>
          <TabsTrigger value="stock">Stok Gudang</TabsTrigger>
          <TabsTrigger value="movements">Riwayat Pergerakan</TabsTrigger>
        </TabsList>

        <TabsContent value="warehouses" className="space-y-6">
          <WarehouseManagement />
        </TabsContent>

        <TabsContent value="stock" className="space-y-6">
          <WarehouseStockOverview />
        </TabsContent>

        <TabsContent value="movements" className="space-y-6">
          <StockMovementHistory />
        </TabsContent>
      </Tabs>
    </div>
  );
};
