import React, { useEffect, useState } from "react";
import { Control, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
  Form,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { SaleFormValues, Customer } from "../types";
import {
  getNextTransactionNumber,
  getNextInvoiceNumber,
} from "@/actions/entities/sales";
import { addCustomer } from "@/actions/entities/customers";
import { createWarehouse } from "@/actions/entities/warehouses";
import {
  Calendar,
  Mail,
  Phone,
  Tag,
  Home,
  Hash,
  FileText,
  User,
  X,
  Loader2,
  Clock,
  Plus,
  ChevronsUpDown,
  RefreshCw,
  WarehouseIcon,
} from "lucide-react";

import {
  Command,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getCustomersAction } from "@/actions/entities/get-customers-action";
import { getWarehouses } from "@/actions/entities/warehouses";
import { Warehouse } from "@/types/warehouse";

// Customer form schema for the dialog
const customerSchema = z.object({
  name: z.string().min(1, "Nama pelanggan wajib diisi"),
  contactName: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),
  notes: z.string().optional(),
});

// Warehouse form schema for the dialog
const warehouseSchema = z.object({
  name: z.string().min(1, "Nama gudang wajib diisi"),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  contactName: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

type CustomerFormData = z.infer<typeof customerSchema>;
type WarehouseFormData = z.infer<typeof warehouseSchema>;

interface SaleInfoSectionProps {
  control: Control<SaleFormValues>;
  isPending: boolean;
  createdAt?: string; // Optional createdAt for edit mode
  setValue?: (name: keyof SaleFormValues, value: any) => void; // Optional setValue function
  trigger?: (
    name?: keyof SaleFormValues | (keyof SaleFormValues)[]
  ) => Promise<boolean>; // Optional trigger function
}

// No default customer

const SaleInfoSection: React.FC<SaleInfoSectionProps> = ({
  control,
  isPending,
  createdAt: _createdAt, // Unused parameter
  setValue,
  trigger,
}) => {
  // State for auto-generation settings
  const [autoTransactionNumber, setAutoTransactionNumber] =
    React.useState(false);
  const [tagInput, setTagInput] = React.useState("");

  // State for actual next transaction numbers
  const [nextTrxNumber, setNextTrxNumber] = React.useState<string>("");
  const [nextInvNumber, setNextInvNumber] = React.useState<string>("");
  const [isLoadingTrx, setIsLoadingTrx] = React.useState(false);
  const [isLoadingInv, setIsLoadingInv] = React.useState(false);
  const [customers, setCustomers] = React.useState<Customer[]>([]);
  const [isLoadingCustomers, setIsLoadingCustomers] = React.useState(true);
  const [warehouses, setWarehouses] = React.useState<Warehouse[]>([]);
  const [isLoadingWarehouses, setIsLoadingWarehouses] = React.useState(true);

  // State for customer dropdown
  const [customerSearchTerm, setCustomerSearchTerm] = useState("");
  const [customerDropdownOpen, setCustomerDropdownOpen] = useState(false);

  // State for customer creation dialog
  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false);
  const customerFieldRef = React.useRef<((id: string) => void) | null>(null);
  const customerEmailFieldRef = React.useRef<((email: string) => void) | null>(
    null
  );
  const customerPhoneFieldRef = React.useRef<((phone: string) => void) | null>(
    null
  );

  // State for warehouse dropdown
  const [warehouseSearchTerm, setWarehouseSearchTerm] = useState("");
  const [warehouseDropdownOpen, setWarehouseDropdownOpen] = useState(false);

  // State for warehouse creation dialog
  const [warehouseDialogOpen, setWarehouseDialogOpen] = useState(false);
  const [isCreatingWarehouse, setIsCreatingWarehouse] = useState(false);
  const warehouseFieldRef = React.useRef<((id: string) => void) | null>(null);

  // Customer form for dialog
  const customerForm = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: "",
      contactName: "",
      email: "",
      phone: "",
      address: "",
      NIK: "",
      NPWP: "",
      notes: "",
    },
  });

  // Warehouse form for dialog
  const warehouseForm = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: "",
      description: "",
      address: "",
      phone: "",
      email: "",
      contactName: "",
      isActive: true,
      isDefault: false,
    },
  });

  // Enhanced filtering - search across multiple fields
  const filteredCustomers = customers.filter((customer) => {
    const searchLower = customerSearchTerm.toLowerCase().trim();
    if (!searchLower) return true;

    return (
      customer.name.toLowerCase().includes(searchLower) ||
      (customer.email &&
        customer.email !== "-" &&
        customer.email.toLowerCase().includes(searchLower)) ||
      (customer.phone &&
        customer.phone !== "-" &&
        customer.phone.toLowerCase().includes(searchLower)) ||
      (customer.NIK &&
        customer.NIK !== "-" &&
        customer.NIK.toLowerCase().includes(searchLower)) ||
      (customer.NPWP &&
        customer.NPWP !== "-" &&
        customer.NPWP.toLowerCase().includes(searchLower))
    );
  });

  // Enhanced filtering for warehouses - search across multiple fields
  const filteredWarehouses = warehouses.filter((warehouse) => {
    const searchLower = warehouseSearchTerm.toLowerCase().trim();
    if (!searchLower) return true;

    return (
      warehouse.name.toLowerCase().includes(searchLower) ||
      (warehouse.description &&
        warehouse.description.toLowerCase().includes(searchLower)) ||
      (warehouse.address &&
        warehouse.address.toLowerCase().includes(searchLower)) ||
      (warehouse.contactName &&
        warehouse.contactName.toLowerCase().includes(searchLower))
    );
  });

  // Generate fallback example transaction numbers for placeholders
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const yearSuffix = String(year).slice(-2);
  const fallbackTrxNumber = `JUAL-${yearSuffix}J000001`;
  const fallbackInvNumber = `INV-${yearSuffix}J000001`;

  // Fetch customers
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setIsLoadingCustomers(true);
        const result = await getCustomersAction();

        if (result.success && result.customers) {
          // Map the customers to include NIK and NPWP fields
          const mappedCustomers = result.customers.map((customer) => ({
            id: customer.id,
            name: customer.name,
            phone: customer.phone || "-",
            email: customer.email || "-",
            address: customer.address || "-",
            NIK: customer.NIK || "-",
            NPWP: customer.NPWP || "-",
          }));

          // Set customers directly without default customer
          setCustomers(mappedCustomers);
        }
      } catch (error) {
        console.error("Error fetching customers:", error);
      } finally {
        setIsLoadingCustomers(false);
      }
    };

    fetchCustomers();
  }, []);

  // Fetch warehouses
  useEffect(() => {
    const fetchWarehouses = async () => {
      try {
        setIsLoadingWarehouses(true);
        const warehouseData = await getWarehouses();
        setWarehouses(warehouseData);

        // Auto-select default warehouse if available and setValue is provided
        if (setValue && warehouseData.length > 0) {
          const defaultWarehouse = warehouseData.find(
            (w) => w.isDefault && w.isActive
          );
          const firstActiveWarehouse = warehouseData.find((w) => w.isActive);

          if (defaultWarehouse) {
            setValue("warehouse", defaultWarehouse.id);
          } else if (firstActiveWarehouse) {
            setValue("warehouse", firstActiveWarehouse.id);
          }
        }
      } catch (error) {
        console.error("Error fetching warehouses:", error);
        toast.error("Gagal memuat data gudang");
      } finally {
        setIsLoadingWarehouses(false);
      }
    };

    fetchWarehouses();
  }, [setValue]);

  // Function to fetch next transaction number
  const fetchNextTransactionNumber = async () => {
    try {
      setIsLoadingTrx(true);
      const result = await getNextTransactionNumber("TRX");
      if (result.success && result.nextNumber) {
        setNextTrxNumber(result.nextNumber);
      }
    } catch (error) {
      console.error("Error fetching next transaction number:", error);
    } finally {
      setIsLoadingTrx(false);
    }
  };

  // Function to fetch next invoice number (only once on mount)
  const fetchNextInvoiceNumber = async () => {
    try {
      setIsLoadingInv(true);
      const result = await getNextInvoiceNumber();
      if (result.success && result.nextNumber) {
        setNextInvNumber(result.nextNumber);
      }
    } catch (error) {
      console.error("Error fetching next invoice number:", error);
    } finally {
      setIsLoadingInv(false);
    }
  };

  // Fetch next transaction number when auto-generation is toggled
  useEffect(() => {
    if (autoTransactionNumber && !nextTrxNumber) {
      fetchNextTransactionNumber();
    }
  }, [autoTransactionNumber, nextTrxNumber]);

  // Fetch initial invoice number on component mount (always auto-generated, only once)
  useEffect(() => {
    fetchNextInvoiceNumber();
  }, []); // Empty dependency array - only run once on mount

  // Update form field when invoice number is generated
  useEffect(() => {
    if (nextInvNumber && setValue) {
      // Update the form field value using setValue prop
      setValue("invoiceRef", nextInvNumber);
    }
  }, [nextInvNumber, setValue]);

  // Customer creation function
  const handleCreateCustomer = async (data: CustomerFormData) => {
    setIsCreatingCustomer(true);
    try {
      // Transform data to match the full customer schema
      const customerData = {
        ...data,
        sameAsShipping: false, // Required field
        billingAddress: data.address || "",
        shippingAddress: data.address || "",
      };

      const result = await addCustomer(customerData);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success && result.customer) {
        toast.success(result.success);
        customerForm.reset();
        setCustomerDialogOpen(false);

        // Add the new customer to the list
        const newCustomer: Customer = {
          id: result.customer.id,
          name: result.customer.name,
          phone: result.customer.phone || "-",
          email: result.customer.email || "-",
          address: result.customer.address || "-",
          NIK: result.customer.NIK || "-",
          NPWP: result.customer.NPWP || "-",
        };

        setCustomers((prev) => [newCustomer, ...prev]);

        // Auto-select the new customer in the main form
        if (customerFieldRef.current) {
          customerFieldRef.current(result.customer.id);
        }

        // Auto-populate customer email, phone and other data using setValue if available
        if (setValue) {
          const emailValue =
            result.customer.email && result.customer.email !== "-"
              ? result.customer.email
              : "";

          setValue("customerEmail", emailValue);

          // Also use the field onChange directly
          if (customerEmailFieldRef.current) {
            customerEmailFieldRef.current(emailValue);
          }

          // Set customer phone if available
          const phoneValue =
            result.customer.phone && result.customer.phone !== "-"
              ? result.customer.phone
              : "";

          setValue("customerPhone", phoneValue);

          // Also use the field onChange directly
          if (customerPhoneFieldRef.current) {
            customerPhoneFieldRef.current(phoneValue);
          }

          // Set customer NIK if available
          if (result.customer.NIK && result.customer.NIK !== "-") {
            setValue("customerNIK", result.customer.NIK);
          } else {
            setValue("customerNIK", "");
          }

          // Set customer NPWP if available
          if (result.customer.NPWP && result.customer.NPWP !== "-") {
            setValue("customerNPWP", result.customer.NPWP);
          } else {
            setValue("customerNPWP", "");
          }

          // Trigger validation to clear any error states
          if (trigger) {
            trigger(["customerId", "customerEmail", "customerPhone"]);
          }
        }
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat menambahkan pelanggan");
      console.error(error);
    } finally {
      setIsCreatingCustomer(false);
    }
  };

  // Wrapper function to handle form submission with event prevention
  const handleCustomerFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    event.stopPropagation();
    customerForm.handleSubmit(handleCreateCustomer)(event);
  };

  // Warehouse creation function
  const handleCreateWarehouse = async (data: WarehouseFormData) => {
    setIsCreatingWarehouse(true);
    try {
      const result = await createWarehouse(data);

      if (result) {
        toast.success("Gudang berhasil dibuat");
        warehouseForm.reset();
        setWarehouseDialogOpen(false);

        // Add the new warehouse to the list
        const newWarehouse: Warehouse = {
          id: result.id,
          name: result.name,
          description: result.description || null,
          address: result.address || null,
          phone: result.phone || null,
          email: result.email || null,
          contactName: result.contactName || null,
          isActive: result.isActive,
          isDefault: result.isDefault,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
          userId: result.userId,
        };

        setWarehouses((prev) => [newWarehouse, ...prev]);

        // Auto-select the new warehouse in the main form
        if (warehouseFieldRef.current) {
          warehouseFieldRef.current(result.id);
        }

        // Also use setValue if available
        if (setValue) {
          setValue("warehouse", result.id);

          // Trigger validation to clear any error states
          if (trigger) {
            trigger(["warehouse"]);
          }
        }
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat menambahkan gudang");
      console.error(error);
    } finally {
      setIsCreatingWarehouse(false);
    }
  };

  // Wrapper function to handle warehouse form submission with event prevention
  const handleWarehouseFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    event.stopPropagation();
    warehouseForm.handleSubmit(handleCreateWarehouse)(event);
  };

  // No need for separate tag handling functions as they're now included in the FormField render prop

  return (
    <div className="space-y-6">
      {/* Main Fields - Horizontal Layout */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Pelanggan */}
        <FormField
          control={control}
          name="customerId"
          render={({ field }) => {
            // Store the field onChange for customer creation
            customerFieldRef.current = field.onChange;

            return (
              <FormItem className="col-span-1">
                <FormLabel className="flex items-center gap-1.5">
                  <User className="h-4 w-4 text-blue-600" />
                  Pelanggan <span className="text-red-500 font-bold">*</span>
                </FormLabel>
                <div className="flex gap-2">
                  <Popover
                    open={customerDropdownOpen}
                    onOpenChange={setCustomerDropdownOpen}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={customerDropdownOpen}
                          className={`flex-1 justify-between hover:bg-accent hover:text-accent-foreground cursor-pointer ${
                            !field.value ? "text-muted-foreground" : ""
                          }`}
                          disabled={isPending}
                        >
                          {isLoadingCustomers ? (
                            <div className="flex items-center">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Memuat pelanggan...
                            </div>
                          ) : field.value ? (
                            <div className="flex items-center">
                              <User className="mr-2 h-4 w-4" />
                              <span>
                                {customers.find(
                                  (customer) => customer.id === field.value
                                )?.name || "Pilih pelanggan"}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <User className="mr-2 h-4 w-4" />
                              <span>Pilih pelanggan</span>
                            </div>
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                      <Command shouldFilter={false}>
                        <CommandInput
                          placeholder="Cari pelanggan..."
                          value={customerSearchTerm}
                          onValueChange={setCustomerSearchTerm}
                          className="h-9"
                          onFocus={() => {
                            if (!customerDropdownOpen) {
                              setCustomerDropdownOpen(true);
                            }
                          }}
                        />
                        <CommandList className="max-h-[300px] overflow-y-auto">
                          <CommandGroup>
                            {/* Show all filtered customers or just 3 if not searching */}
                            {(customerSearchTerm
                              ? filteredCustomers
                              : customers.slice(0, 3)
                            ).map((customer) => (
                              <div key={customer.id} className="px-2">
                                <button
                                  type="button"
                                  className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                                  onClick={() => {
                                    field.onChange(customer.id);
                                    setCustomerDropdownOpen(false);
                                    setCustomerSearchTerm("");

                                    // Auto-populate customer data using setValue if available
                                    const emailValue =
                                      customer.email && customer.email !== "-"
                                        ? customer.email
                                        : "";

                                    const phoneValue =
                                      customer.phone && customer.phone !== "-"
                                        ? customer.phone
                                        : "";

                                    // Use both setValue and direct field onChange
                                    if (setValue) {
                                      setValue("customerEmail", emailValue);
                                      setValue("customerPhone", phoneValue);
                                    }

                                    // Also use the field onChange directly
                                    if (customerEmailFieldRef.current) {
                                      customerEmailFieldRef.current(emailValue);
                                    }

                                    if (customerPhoneFieldRef.current) {
                                      customerPhoneFieldRef.current(phoneValue);
                                    }

                                    // Set other customer data if setValue is available
                                    if (setValue) {
                                      // Set customer NIK if available
                                      if (
                                        customer.NIK &&
                                        customer.NIK !== "-"
                                      ) {
                                        setValue("customerNIK", customer.NIK);
                                      } else {
                                        setValue("customerNIK", "");
                                      }

                                      // Set customer NPWP if available
                                      if (
                                        customer.NPWP &&
                                        customer.NPWP !== "-"
                                      ) {
                                        setValue("customerNPWP", customer.NPWP);
                                      } else {
                                        setValue("customerNPWP", "");
                                      }

                                      // Trigger validation to clear any error states
                                      if (trigger) {
                                        trigger([
                                          "customerId",
                                          "customerEmail",
                                          "customerPhone",
                                        ]);
                                      }
                                    }
                                  }}
                                >
                                  <User className="mr-2 h-4 w-4 shrink-0 text-blue-500" />
                                  <div className="flex flex-col flex-grow">
                                    <span className="font-medium">
                                      {customer.name}
                                    </span>
                                    {customer.email &&
                                      customer.email !== "-" && (
                                        <span className="text-xs text-muted-foreground">
                                          {customer.email}
                                        </span>
                                      )}
                                  </div>
                                  <span className="text-xs text-blue-500 ml-2">
                                    Pilih
                                  </span>
                                </button>
                              </div>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  {/* Add Customer Button */}
                  <Dialog
                    open={customerDialogOpen}
                    onOpenChange={setCustomerDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="shrink-0 cursor-pointer"
                        type="button"
                        disabled={isPending}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Tambah Pelanggan Baru</DialogTitle>
                        <DialogDescription>
                          Buat pelanggan baru untuk transaksi ini.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...customerForm}>
                        <form
                          onSubmit={handleCustomerFormSubmit}
                          className="space-y-4"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Customer Name */}
                            <FormField
                              control={customerForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>
                                    Nama Pelanggan{" "}
                                    <span className="text-red-500">*</span>
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Masukkan nama pelanggan"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Contact Name */}
                            <FormField
                              control={customerForm.control}
                              name="contactName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nama Kontak</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nama kontak person"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Email */}
                            <FormField
                              control={customerForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="email"
                                      placeholder="<EMAIL>"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Phone */}
                            <FormField
                              control={customerForm.control}
                              name="phone"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nomor Telepon</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="08xxxxxxxxxx"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* NIK */}
                            <FormField
                              control={customerForm.control}
                              name="NIK"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>NIK</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nomor Induk Kependudukan"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* NPWP */}
                            <FormField
                              control={customerForm.control}
                              name="NPWP"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>NPWP</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nomor Pokok Wajib Pajak"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Address */}
                            <FormField
                              control={customerForm.control}
                              name="address"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>Alamat</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Alamat lengkap pelanggan"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                      className="resize-none"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Notes */}
                            <FormField
                              control={customerForm.control}
                              name="notes"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>Catatan</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Catatan tambahan"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                      className="resize-none"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <DialogFooter>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                setCustomerDialogOpen(false);
                              }}
                              disabled={isCreatingCustomer}
                            >
                              Batal
                            </Button>
                            <Button
                              type="submit"
                              className="cursor-pointer"
                              disabled={isCreatingCustomer}
                              onClick={(e) => {
                                // Additional safeguard to prevent event bubbling
                                e.stopPropagation();
                              }}
                            >
                              {isCreatingCustomer ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Menyimpan...
                                </>
                              ) : (
                                "Simpan"
                              )}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        {/* Customer Email and Phone */}
        <div className="col-span-1 grid grid-cols-1 gap-2 md:grid-cols-2">
          <FormField
            control={control}
            name="customerEmail"
            render={({ field }) => {
              // Store the field onChange for customer selection
              customerEmailFieldRef.current = field.onChange;

              return (
                <FormItem>
                  <FormLabel className="flex items-center gap-1.5">
                    <Mail className="h-4 w-4 text-red-600" />
                    Email Pelanggan
                    <span className="text-red-500 font-bold">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter email"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />

          <FormField
            control={control}
            name="customerPhone"
            render={({ field }) => {
              // Store the field onChange for customer selection
              customerPhoneFieldRef.current = field.onChange;

              return (
                <FormItem>
                  <FormLabel className="flex items-center gap-1.5">
                    <Phone className="h-4 w-4 text-green-600" />
                    Nomor Handphone
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="08123456789"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </div>

        {/* Transaction Date */}
        <FormField
          control={control}
          name="transactionDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-green-600" />
                Tgl. Transaksi
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : new Date()}
                  setDate={(date) => onChange(date || new Date())}
                  placeholder="Pilih tanggal transaksi"
                  disabled={isPending}
                  className="w-full cursor-pointer"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Payment Due Date */}
        <FormField
          control={control}
          name="paymentDueDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Clock className="h-4 w-4 text-yellow-600" />
                Tgl. Jatuh Tempo
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : undefined}
                  setDate={(date) => onChange(date)}
                  placeholder="Pilih tanggal jatuh tempo"
                  disabled={isPending}
                  className="w-full cursor-pointer"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transaction Number */}
        <FormField
          control={control}
          name="transactionNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Hash className="h-4 w-4 text-purple-600" />
                Nomor Transaksi
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoTransactionNumber
                        ? isLoadingTrx
                          ? "Loading..."
                          : `Auto - ${nextTrxNumber || fallbackTrxNumber}`
                        : `Contoh: JUAL-${year.toString().slice(-2)}J000001`
                    }
                    {...field}
                    disabled={isPending || autoTransactionNumber}
                    value={autoTransactionNumber ? "" : field.value}
                    onChange={(e) => {
                      if (!autoTransactionNumber) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingTrx && autoTransactionNumber ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 cursor-pointer"
                    onClick={() => {
                      setAutoTransactionNumber(!autoTransactionNumber);
                      if (!autoTransactionNumber) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoTransactionNumber
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <RefreshCw className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoTransactionNumber && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor transaksi akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Invoice Reference */}
        <FormField
          control={control}
          name="invoiceRef"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <FileText className="h-4 w-4 text-yellow-600" />
                Nomor Invoice
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      isLoadingInv
                        ? "Loading..."
                        : `Auto - ${nextInvNumber || fallbackInvNumber}`
                    }
                    {...field}
                    disabled={true} // Always disabled - auto-generated only
                    value={nextInvNumber || ""}
                    readOnly
                    onChange={() => {}} // Prevent any changes
                  />
                </FormControl>
                {isLoadingInv && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Nomor invoice digenerate otomatis saat form dibuat.
              </p>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Warehouse */}
        <FormField
          control={control}
          name="warehouse"
          render={({ field }) => {
            // Store the field onChange for warehouse creation
            warehouseFieldRef.current = field.onChange;

            return (
              <FormItem className="col-span-1">
                <FormLabel className="flex items-center gap-1.5">
                  <WarehouseIcon className="h-4 w-4 text-violet-600" />
                  Gudang
                </FormLabel>
                <div className="flex gap-2">
                  <Popover
                    open={warehouseDropdownOpen}
                    onOpenChange={setWarehouseDropdownOpen}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={warehouseDropdownOpen}
                          className={`flex-1 justify-between hover:bg-accent hover:text-accent-foreground cursor-pointer ${
                            !field.value ? "text-muted-foreground" : ""
                          }`}
                          disabled={isPending}
                        >
                          {isLoadingWarehouses ? (
                            <div className="flex items-center">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Memuat gudang...
                            </div>
                          ) : field.value ? (
                            <div className="flex items-center">
                              <WarehouseIcon className="mr-2 h-4 w-4" />
                              <span>
                                {warehouses.find(
                                  (warehouse) => warehouse.id === field.value
                                )?.name || "Pilih gudang"}
                              </span>
                              {warehouses.find(
                                (warehouse) => warehouse.id === field.value
                              )?.isDefault && (
                                <Badge
                                  variant="secondary"
                                  className="text-xs ml-2"
                                >
                                  Default
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <WarehouseIcon className="mr-2 h-4 w-4" />
                              <span>Pilih gudang</span>
                            </div>
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                      <Command shouldFilter={false}>
                        <CommandInput
                          placeholder="Cari gudang..."
                          value={warehouseSearchTerm}
                          onValueChange={setWarehouseSearchTerm}
                          className="h-9"
                          onFocus={() => {
                            if (!warehouseDropdownOpen) {
                              setWarehouseDropdownOpen(true);
                            }
                          }}
                        />
                        <CommandList className="max-h-[300px] overflow-y-auto">
                          <CommandGroup>
                            {/* Show all filtered warehouses or just 3 if not searching */}
                            {(warehouseSearchTerm
                              ? filteredWarehouses.filter((w) => w.isActive)
                              : warehouses.filter((w) => w.isActive).slice(0, 3)
                            ).map((warehouse) => (
                              <div key={warehouse.id} className="px-2">
                                <button
                                  type="button"
                                  className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                                  onClick={() => {
                                    field.onChange(warehouse.id);
                                    setWarehouseDropdownOpen(false);
                                    setWarehouseSearchTerm("");
                                  }}
                                >
                                  <WarehouseIcon className="mr-2 h-4 w-4 shrink-0 text-violet-500" />
                                  <div className="flex flex-col flex-grow">
                                    <div className="flex items-center gap-2">
                                      <span className="font-medium">
                                        {warehouse.name}
                                      </span>
                                      {warehouse.isDefault && (
                                        <Badge
                                          variant="secondary"
                                          className="text-xs"
                                        >
                                          Default
                                        </Badge>
                                      )}
                                    </div>
                                    {warehouse.description && (
                                      <span className="text-xs text-muted-foreground">
                                        {warehouse.description}
                                      </span>
                                    )}
                                  </div>
                                  <span className="text-xs text-violet-500 ml-2">
                                    Pilih
                                  </span>
                                </button>
                              </div>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  {/* Add Warehouse Button */}
                  <Dialog
                    open={warehouseDialogOpen}
                    onOpenChange={setWarehouseDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="shrink-0 cursor-pointer"
                        type="button"
                        disabled={isPending}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Tambah Gudang Baru</DialogTitle>
                        <DialogDescription>
                          Buat gudang baru untuk transaksi ini.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...warehouseForm}>
                        <form
                          onSubmit={handleWarehouseFormSubmit}
                          className="space-y-4"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Warehouse Name */}
                            <FormField
                              control={warehouseForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>
                                    Nama Gudang{" "}
                                    <span className="text-red-500">*</span>
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Masukkan nama gudang"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Description */}
                            <FormField
                              control={warehouseForm.control}
                              name="description"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>Deskripsi</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Deskripsi gudang (opsional)"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                      className="resize-none"
                                      rows={3}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Contact Name */}
                            <FormField
                              control={warehouseForm.control}
                              name="contactName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nama Kontak</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nama kontak person"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Phone */}
                            <FormField
                              control={warehouseForm.control}
                              name="phone"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nomor Telepon</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="08xxxxxxxxxx"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Email */}
                            <FormField
                              control={warehouseForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>Email</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="email"
                                      placeholder="<EMAIL>"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Address */}
                            <FormField
                              control={warehouseForm.control}
                              name="address"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>Alamat</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Alamat lengkap gudang"
                                      {...field}
                                      disabled={isCreatingWarehouse}
                                      className="resize-none"
                                      rows={3}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <DialogFooter>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                setWarehouseDialogOpen(false);
                              }}
                              disabled={isCreatingWarehouse}
                            >
                              Batal
                            </Button>
                            <Button
                              type="submit"
                              className="cursor-pointer"
                              disabled={isCreatingWarehouse}
                              onClick={(e) => {
                                // Additional safeguard to prevent event bubbling
                                e.stopPropagation();
                              }}
                            >
                              {isCreatingWarehouse ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Menyimpan...
                                </>
                              ) : (
                                "Simpan"
                              )}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        {/* Tags */}
        <FormField
          control={control}
          name="tags"
          render={({ field }) => {
            const handleAddTag = () => {
              if (tagInput.trim()) {
                // Check if we already have 3 tags
                if ((field.value || []).length >= 3) {
                  return; // Don't add more tags
                }

                // Check if the tag already exists
                if (!field.value?.includes(tagInput.trim())) {
                  const newTags = [...(field.value || []), tagInput.trim()];
                  field.onChange(newTags);
                }
                setTagInput("");
              }
            };

            const handleKeyDown = (e: React.KeyboardEvent) => {
              if (e.key === "Enter" || e.key === ",") {
                e.preventDefault();

                // If comma is pressed, we might have multiple tags
                if (e.key === ",") {
                  const tags = tagInput
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag !== "");

                  if (tags.length > 0) {
                    // Add all tags that don't already exist
                    const existingTags = field.value || [];
                    const newTags = [...existingTags];

                    tags.forEach((tag) => {
                      if (!existingTags.includes(tag)) {
                        newTags.push(tag);
                      }
                    });

                    field.onChange(newTags);
                    setTagInput("");
                    return;
                  }
                }

                handleAddTag();
              }
            };

            return (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Tag className="h-4 w-4 text-teal-600" />
                  Tag Penjualan
                </FormLabel>

                {/* Input for new tags - Moved to top */}
                <div className="flex gap-2 mb-3">
                  <FormControl>
                    <Input
                      placeholder={
                        (field.value || []).length >= 3
                          ? "Maksimal 3 tag telah tercapai"
                          : "Tambahkan tag (tekan Enter atau koma)"
                      }
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      disabled={isPending || (field.value || []).length >= 3}
                      className="flex-1"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={handleAddTag}
                    disabled={
                      isPending ||
                      !tagInput.trim() ||
                      (field.value || []).length >= 3
                    }
                    className="border-teal-200 hover:bg-teal-100 hover:text-teal-800 cursor-pointer"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Tambah
                  </Button>
                </div>

                {/* Display existing tags - Moved to bottom */}
                <div className="flex flex-wrap gap-2">
                  {field.value && field.value.length > 0 ? (
                    field.value.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 flex items-center gap-1 shadow-sm"
                      >
                        {tag}
                        <button
                          type="button"
                          className="ml-1 hover:text-red-500 cursor-pointer"
                          onClick={() => {
                            const newTags = [...(field.value || [])];
                            newTags.splice(index, 1);
                            field.onChange(newTags);
                          }}
                          disabled={isPending}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      Belum ada tag
                    </span>
                  )}
                </div>

                <FormDescription>
                  Tag untuk memudahkan pencarian dan pengelompokan
                </FormDescription>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </div>

      {/* Shipping Address - Full Width */}
      <div>
        <FormField
          control={control}
          name="shippingAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Home className="h-4 w-4 text-orange-600" />
                Alamat Penagihan
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g. Jalan Indonesia Blok C No. 22"
                  className="resize-none"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default SaleInfoSection;
