"use client";

import type { NextPage } from "next";
import Head from "next/head";
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowsUpDownIcon,
} from "@heroicons/react/24/outline";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { deleteSale } from "@/actions/entities/sales";

// Import types and components
import { Sale, SaleCounts, ColumnVisibility } from "./types";
import { SaleSummaryCards } from "./components/SaleSummaryCards";
import { SaleActions } from "./components/SaleActions";
import { SaleTableDesktop } from "./components/SaleTableDesktop";
import { SalesFilterState } from "./components/SalesFilter";

interface SalesPageProps {
  sales: Sale[];
  customers?: Array<{ id: string; name: string }>;
  warehouses?: Array<{ id: string; name: string }>;
}

const SalesPage: NextPage<SalesPageProps> = ({
  sales,
  customers = [],
  warehouses = [],
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [subTab, setSubTab] = useState(() => {
    // Check if there's a tab parameter in the URL
    const tabParam = searchParams.get("tab");
    return tabParam === "drafts" ? "drafts" : "all-sales";
  });
  const [filteredSales, setFilteredSales] = useState<Sale[]>(sales);

  // Filter state
  const [filters, setFilters] = useState<SalesFilterState>({
    customer: undefined,
    dateRange: { start: undefined, end: undefined },
    amountRange: { min: undefined, max: undefined },
    status: "all",
    hasInvoice: undefined,
    warehouse: undefined,
    isWholesale: undefined,
  });
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedSales, setSelectedSales] = useState<string[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [paginatedSales, setPaginatedSales] = useState<Sale[]>([]);

  // Helper function to get default column visibility
  const getDefaultColumnVisibility = (): ColumnVisibility => {
    return {
      id: true,
      date: true,
      paymentDueDate: true,
      customer: true,
      totalAmount: true,
      itemCount: true,
      invoiceRef: true,
      tags: true,
      totalQuantity: true, // Added missing property
    };
  };

  // Column visibility state with localStorage persistence
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(
    () => {
      // Try to get saved column visibility from localStorage
      if (typeof window !== "undefined") {
        try {
          const savedVisibility = localStorage.getItem("salesColumnVisibility");
          if (savedVisibility) {
            const parsed = JSON.parse(savedVisibility) as ColumnVisibility;

            // Validate that all required keys exist in the saved data
            const defaultVisibility = getDefaultColumnVisibility();
            const hasAllKeys = Object.keys(defaultVisibility).every(
              (key) => key in parsed
            );

            if (hasAllKeys) {
              console.log(
                "Using saved column visibility from localStorage:",
                parsed
              );
              return parsed;
            } else {
              // If saved data is incomplete, remove it and use defaults
              localStorage.removeItem("salesColumnVisibility");
              console.warn(
                "Incomplete column visibility data found, using defaults"
              );
            }
          }
        } catch (error) {
          console.error("Failed to parse saved column visibility:", error);
          // Clear corrupted data
          localStorage.removeItem("salesColumnVisibility");
        }
      }

      // Default column visibility for new users or when localStorage is invalid
      const defaultVisibility = getDefaultColumnVisibility();
      console.log(
        "Using default column visibility for new user:",
        defaultVisibility
      );
      return defaultVisibility;
    }
  );

  // Update URL when tab changes
  useEffect(() => {
    const currentParams = new URLSearchParams(window.location.search);
    if (subTab === "drafts") {
      currentParams.set("tab", "drafts");
    } else {
      currentParams.delete("tab");
    }
    const newUrl = `${window.location.pathname}${currentParams.toString() ? `?${currentParams.toString()}` : ""}`;
    window.history.replaceState({}, "", newUrl);
  }, [subTab]);

  // Calculate sale counts for the summary cards
  const saleCounts: SaleCounts = {
    total: sales.length,
    today: sales.filter((s) => {
      const now = new Date();
      const saleDate = new Date(s.saleDate);
      return (
        saleDate.getDate() === now.getDate() &&
        saleDate.getMonth() === now.getMonth() &&
        saleDate.getFullYear() === now.getFullYear()
      );
    }).length,
    thisMonth: sales.filter((s) => {
      const now = new Date();
      const saleDate = new Date(s.saleDate);
      return (
        saleDate.getMonth() === now.getMonth() &&
        saleDate.getFullYear() === now.getFullYear()
      );
    }).length,
    pending: 0, // This would normally be calculated from the data
    drafts: sales.filter((sale) => sale.isDraft).length,
  };

  // Filter sales based on search term and tab
  useEffect(() => {
    // Reset to first page when search term changes
    setCurrentPage(1);

    // Start with all sales
    let filtered = [...sales];

    // Apply sub-tab filters
    if (subTab === "all-sales") {
      // Show only published sales (not drafts)
      filtered = filtered.filter((sale) => !sale.isDraft);
    } else if (subTab === "drafts") {
      // Show only draft sales
      filtered = filtered.filter((sale) => sale.isDraft);
    }

    // Apply search term filter
    if (searchTerm.trim()) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = filtered.filter((sale) => {
        return (
          sale.id.toLowerCase().includes(lowercasedTerm) ||
          (sale.transactionNumber &&
            sale.transactionNumber.toLowerCase().includes(lowercasedTerm))
        );
      });
    }

    // Apply advanced filters
    if (filters.customer) {
      filtered = filtered.filter(
        (sale) => sale.customerId === filters.customer
      );
    }

    if (filters.dateRange?.start) {
      filtered = filtered.filter(
        (sale) => new Date(sale.saleDate) >= filters.dateRange!.start!
      );
    }

    if (filters.dateRange?.end) {
      filtered = filtered.filter(
        (sale) => new Date(sale.saleDate) <= filters.dateRange!.end!
      );
    }

    if (filters.amountRange?.min !== undefined) {
      filtered = filtered.filter(
        (sale) => sale.totalAmount >= filters.amountRange!.min!
      );
    }

    if (filters.amountRange?.max !== undefined) {
      filtered = filtered.filter(
        (sale) => sale.totalAmount <= filters.amountRange!.max!
      );
    }

    if (filters.status && filters.status !== "all") {
      switch (filters.status) {
        case "completed":
          filtered = filtered.filter((sale) => !sale.isDraft);
          break;
        case "draft":
          filtered = filtered.filter((sale) => sale.isDraft);
          break;
      }
    }

    if (filters.hasInvoice !== undefined) {
      filtered = filtered.filter((sale) =>
        filters.hasInvoice ? !!sale.invoiceRef : !sale.invoiceRef
      );
    }

    if (filters.warehouse) {
      filtered = filtered.filter(
        (sale) => sale.warehouseId === filters.warehouse
      );
    }

    if (filters.isWholesale !== undefined) {
      filtered = filtered.filter((sale) => {
        // Check if any sale item has wholesale pricing
        return (
          sale.items?.some(
            (item) => item.isWholesale === filters.isWholesale
          ) || false
        );
      });
    }

    setFilteredSales(filtered);
  }, [searchTerm, sales, subTab, filters]);

  // Apply pagination to filtered sales
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedSales(filteredSales.slice(startIndex, endIndex));
  }, [filteredSales, currentPage, itemsPerPage]);

  // Save column visibility to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(
          "salesColumnVisibility",
          JSON.stringify(columnVisibility)
        );
      } catch (error) {
        console.error(
          "Failed to save column visibility to localStorage:",
          error
        );
      }
    }
  }, [columnVisibility]);

  // Utility function to reset column visibility to defaults
  const resetColumnVisibility = () => {
    const defaultVisibility = getDefaultColumnVisibility();
    setColumnVisibility(defaultVisibility);

    // Also clear from localStorage to ensure clean state
    if (typeof window !== "undefined") {
      try {
        localStorage.removeItem("salesColumnVisibility");
      } catch (error) {
        console.error(
          "Failed to clear column visibility from localStorage:",
          error
        );
      }
    }
  };

  // Sort sales
  const handleSort = (field: string) => {
    // Reset to first page when sorting changes
    setCurrentPage(1);

    if (sortField === field) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }

    // Sort the filtered sales
    const sortedSales = [...filteredSales].sort((a, b) => {
      let aValue, bValue;

      // Handle special case for itemCount which is derived
      if (field === "itemCount") {
        aValue = a.items.length;
        bValue = b.items.length;
      } else {
        // Handle other fields
        aValue = a[field as keyof Sale] || "";
        bValue = b[field as keyof Sale] || "";
      }

      // Compare values
      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    setFilteredSales(sortedSales);
  };

  // Get sort icon based on current sort state
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowsUpDownIcon className="h-4 w-4 ml-1" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUpIcon className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDownIcon className="h-4 w-4 ml-1" />
    );
  };

  // Handle batch delete of selected sales
  const handleBatchDelete = async () => {
    if (selectedSales.length === 0) return;

    try {
      // Delete each selected sale
      for (const id of selectedSales) {
        const result = await deleteSale(id);
        if (result.error) {
          toast.error(`Gagal menghapus penjualan: ${result.error}`);
        }
      }

      // Show success message
      toast.success(`${selectedSales.length} penjualan berhasil dihapus`);

      // Clear selection
      setSelectedSales([]);

      // Refresh the page
      router.refresh();
    } catch (error) {
      console.error("Error deleting sales:", error);
      toast.error("Terjadi kesalahan saat menghapus penjualan.");
    }
  };

  return (
    <>
      <Head>
        <title>Penjualan - Kasir Online</title>
      </Head>

      <div className="space-y-6">
        {/* Sale Status Summary Cards */}
        <SaleSummaryCards saleCounts={saleCounts} />

        {/* Sub Tabs */}
        <Tabs
          defaultValue="all-sales"
          value={subTab}
          onValueChange={setSubTab}
          className="w-full"
        >
          <TabsList className="mb-4">
            <TabsTrigger value="all-sales">Daftar Penjualan</TabsTrigger>
            <TabsTrigger value="drafts">Draf Penjualan</TabsTrigger>
          </TabsList>

          <TabsContent value="all-sales" className="space-y-6">
            {/* Header Actions */}
            <SaleActions
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              filters={filters}
              onFilterChange={setFilters}
              customers={customers}
              warehouses={warehouses}
              selectedSales={selectedSales}
              onBatchDelete={handleBatchDelete}
              onRefresh={() => window.location.reload()}
            />

            {/* Sales List */}
            <div className="overflow-x-auto">
              {/* Table View */}
              <SaleTableDesktop
                sales={paginatedSales}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                searchTerm={searchTerm}
                selectedSales={selectedSales}
                setSelectedSales={setSelectedSales}
              />
            </div>

            {/* Pagination - Moved outside the overflow container */}
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredSales.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                totalItems={filteredSales.length}
              />
            </div>
          </TabsContent>

          <TabsContent value="drafts" className="space-y-6">
            {/* Header Actions */}
            <SaleActions
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              filters={filters}
              onFilterChange={setFilters}
              customers={customers}
              warehouses={warehouses}
              selectedSales={selectedSales}
              onBatchDelete={handleBatchDelete}
              onRefresh={() => window.location.reload()}
            />

            {/* Sales List */}
            <div className="overflow-x-auto">
              {/* Table View */}
              <SaleTableDesktop
                sales={paginatedSales}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                searchTerm={searchTerm}
                selectedSales={selectedSales}
                setSelectedSales={setSelectedSales}
              />
            </div>

            {/* Pagination - Moved outside the overflow container */}
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredSales.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                totalItems={filteredSales.length}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default SalesPage;
