"use client";

import { useState, useEffect } from "react";
import {
  CreditCard,
  Receipt,
  Clock,
  <PERSON><PERSON><PERSON>,
  CreditCard as CreditCardIcon,
} from "lucide-react";

import PaymentHistory from "@/components/subscription/payment-history";
import CurrentSubscription from "@/components/subscription/current-subscription";
import { SubscriptionPlan } from "@prisma/client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";

interface BillingSettingsProps {
  initialData?: {
    plan: SubscriptionPlan;
    expiryDate: string | null;
    isActive: boolean;
  };
}

export default function BillingSettings({ initialData }: BillingSettingsProps) {
  const [subscription, setSubscription] = useState(initialData);
  const [isLoading, setIsLoading] = useState(!initialData);

  // Fetch subscription data if not provided
  const fetchSubscription = async () => {
    if (initialData) return;

    try {
      setIsLoading(true);
      const response = await fetch("/api/subscriptions");

      if (!response.ok) {
        throw new Error("Failed to fetch subscription");
      }

      const data = await response.json();
      setSubscription(data.subscription);
    } catch (error) {
      console.error("Error fetching subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load subscription on component mount if not provided
  useEffect(() => {
    fetchSubscription();
  }, [initialData]);

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Tidak ada";
    return new Date(dateString).toLocaleDateString("id-ID", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Get plan details
  const getPlanFeatures = (plan: SubscriptionPlan | undefined) => {
    if (!plan) return [];
    return SUBSCRIPTION_PLANS[plan].features.slice(0, 3); // Get first 3 features
  };

  return (
    <div className="space-y-6">
      {/* Modern Header */}
      <Card className="border-none shadow-sm bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader className="pb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <CreditCard className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Tagihan & Langganan</h2>
                <p className="text-sm text-muted-foreground">
                  Kelola langganan dan metode pembayaran Anda
                </p>
              </div>
            </div>

            {subscription && (
              <div className="flex items-center gap-2 px-3 py-1.5 bg-primary/10 rounded-full text-sm">
                <span>Paket Saat Ini:</span>
                <Badge variant="outline" className="font-medium bg-background">
                  {SUBSCRIPTION_PLANS[subscription.plan].name}
                </Badge>
              </div>
            )}
          </div>
        </CardHeader>

        {/* Subscription Summary */}
        {subscription && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
              <div className="flex items-center gap-3 p-3 bg-background rounded-lg border shadow-sm">
                <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                  <Clock className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Aktif Hingga</p>
                  <p className="font-medium">
                    {subscription.expiryDate
                      ? formatDate(subscription.expiryDate)
                      : "Selamanya"}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-background rounded-lg border shadow-sm">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <CreditCardIcon className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Harga</p>
                  <p className="font-medium">
                    {SUBSCRIPTION_PLANS[subscription.plan].price > 0
                      ? new Intl.NumberFormat("id-ID", {
                          style: "currency",
                          currency: "IDR",
                          minimumFractionDigits: 0,
                        }).format(SUBSCRIPTION_PLANS[subscription.plan].price)
                      : "Gratis"}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-background rounded-lg border shadow-sm">
                <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                  <BarChart className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Status</p>
                  <p className="font-medium">
                    {subscription.isActive ? "Aktif" : "Tidak Aktif"}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Current Subscription */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">Langganan Saat Ini</h3>
          {subscription?.isActive && (
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200"
            >
              Aktif
            </Badge>
          )}
        </div>
        <p className="text-sm text-muted-foreground">
          Informasi langganan dan status pembayaran Anda
        </p>

        <CurrentSubscription initialData={subscription} />
      </div>

      {/* Payment History */}
      <div className="space-y-4 pt-6 border-t">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">Riwayat Pembayaran</h3>
            <p className="text-sm text-muted-foreground">
              Lihat riwayat pembayaran dan status langganan Anda
            </p>
          </div>
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            Transaksi Terbaru
          </Badge>
        </div>

        <PaymentHistory />
      </div>
    </div>
  );
}
