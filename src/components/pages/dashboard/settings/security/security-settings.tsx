"use client";

import { useState, useEffect } from "react";
import {
  ShieldCheck,
  AlertCircle,
  CheckCircle,
  Key,
  Clock,
  Bell,
  Laptop,
  Smartphone,
  Tablet,
  LogOut,
  Info,
  Loader2,
  Eye,
  EyeOff,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { signOut } from "next-auth/react";
import {
  updatePassword,
  checkUserPasswordStatus,
} from "@/actions/auth/update-password";
import {
  DeviceSession,
  getDeviceSessions,
  revokeAllSessionsIncludingCurrent,
  revokeSession,
} from "@/actions/users/sessions";
import {
  getSessionSettings,
  SessionSettings,
  updateSessionSettings,
} from "@/actions/users/session-settings";

export default function SecuritySettings() {
  // Security settings state
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [sessionTimeout, setSessionTimeout] = useState("30");
  const [loginNotifications, setLoginNotifications] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [originalSettings, setOriginalSettings] = useState<SessionSettings>({
    sessionTimeout: "30",
    loginNotifications: true,
  });
  const [hasChanges, setHasChanges] = useState(false);

  // Device management state
  const [devices, setDevices] = useState<
    (DeviceSession & { isLoggingOut: boolean })[]
  >([]);
  const [isLoadingDevices, setIsLoadingDevices] = useState(true);
  const [isLoggingOutAll, setIsLoggingOutAll] = useState(false);

  // Password form state
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);

  // Password form values
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // User password status
  const [userPasswordStatus, setUserPasswordStatus] = useState<{
    hasPassword: boolean;
    isGoogleUser: boolean;
    needsCurrentPassword: boolean;
    provider: string | null;
  } | null>(null);
  const [isCheckingPasswordStatus, setIsCheckingPasswordStatus] =
    useState(true);

  // Check user password status on component mount
  useEffect(() => {
    const checkPasswordStatus = async () => {
      try {
        setIsCheckingPasswordStatus(true);
        const result = await checkUserPasswordStatus();
        if (result.success) {
          setUserPasswordStatus({
            hasPassword: result.hasPassword,
            isGoogleUser: result.isGoogleUser,
            needsCurrentPassword: result.needsCurrentPassword,
            provider: result.provider,
          });
        }
      } catch (error) {
        console.error("Error checking password status:", error);
      } finally {
        setIsCheckingPasswordStatus(false);
      }
    };

    checkPasswordStatus();
  }, []);

  // Calculate password strength when newPassword changes
  useEffect(() => {
    if (!newPassword) {
      setPasswordStrength(0);
      return;
    }

    let strength = 0;
    if (newPassword.length >= 8) strength += 25; // Length
    if (/[A-Z]/.test(newPassword)) strength += 25; // Uppercase
    if (/[0-9]/.test(newPassword)) strength += 25; // Number
    if (/[^A-Za-z0-9]/.test(newPassword)) strength += 25; // Special char

    setPasswordStrength(strength);
  }, [newPassword]);

  const handlePasswordSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsPasswordLoading(true);
    setPasswordSuccess(null);
    setPasswordError(null);

    try {
      // For Google OAuth users, don't send current password
      const passwordData = userPasswordStatus?.needsCurrentPassword
        ? { currentPassword, newPassword, confirmPassword }
        : { currentPassword: "", newPassword, confirmPassword };

      const result = await updatePassword(passwordData);

      if (result.error) {
        setPasswordError(result.error);
      } else {
        const successMessage = userPasswordStatus?.hasPassword
          ? "Password berhasil diperbarui!"
          : "Password berhasil dibuat!";
        setPasswordSuccess(result.success || successMessage);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");
        setPasswordStrength(0); // Reset strength indicator
      }
    } catch (err) {
      setPasswordError("Terjadi kesalahan saat memperbarui password.");
      console.error(err);
    } finally {
      setIsPasswordLoading(false);
    }
  };

  const handleResetPassword = () => {
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setPasswordSuccess(null);
    setPasswordError(null);
    setPasswordStrength(0);
  };

  // Fetch device sessions and session settings on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch device sessions
        setIsLoadingDevices(true);
        const sessions = await getDeviceSessions();
        setDevices(
          sessions.map((session) => ({ ...session, isLoggingOut: false }))
        );

        // Fetch session settings
        setIsLoadingSettings(true);
        const settingsResult = await getSessionSettings();
        if (settingsResult.success && settingsResult.data) {
          const { sessionTimeout: timeout, loginNotifications: notifications } =
            settingsResult.data;
          setSessionTimeout(timeout);
          setLoginNotifications(notifications);
          setOriginalSettings(settingsResult.data);
        } else if (settingsResult.message) {
          console.error(
            "Error fetching session settings:",
            settingsResult.message
          );
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Gagal memuat data");
      } finally {
        setIsLoadingDevices(false);
        setIsLoadingSettings(false);
      }
    };

    fetchData();
  }, []);

  // Handle logout from a specific device
  const handleLogoutDevice = async (deviceId: string) => {
    // Update device state to show loading
    setDevices((prevDevices) =>
      prevDevices.map((device) =>
        device.id === deviceId ? { ...device, isLoggingOut: true } : device
      )
    );

    try {
      const result = await revokeSession(deviceId);

      if (result.success) {
        if (result.message === "current_session") {
          toast.info(
            "Anda akan logout dari perangkat ini dalam beberapa detik"
          );
          // Wait a moment before logging out to show the toast
          setTimeout(() => {
            signOut({ callbackUrl: "/login" });
          }, 2000);
        } else {
          toast.success("Berhasil logout dari perangkat");
          // Remove the device from the list
          setDevices((prevDevices) =>
            prevDevices.filter((device) => device.id !== deviceId)
          );
        }
      } else {
        toast.error(result.message);
        // Reset the loading state
        setDevices((prevDevices) =>
          prevDevices.map((device) =>
            device.id === deviceId ? { ...device, isLoggingOut: false } : device
          )
        );
      }
    } catch (error) {
      console.error("Error logging out device:", error);
      toast.error("Terjadi kesalahan saat logout dari perangkat");
      // Reset the loading state
      setDevices((prevDevices) =>
        prevDevices.map((device) =>
          device.id === deviceId ? { ...device, isLoggingOut: false } : device
        )
      );
    }
  };

  // Handle logout from all devices
  const handleLogoutAllDevices = async () => {
    setIsLoggingOutAll(true);

    try {
      const result = await revokeAllSessionsIncludingCurrent();

      if (result.success) {
        if (result.message === "all_sessions") {
          toast.info(
            "Anda akan logout dari semua perangkat dalam beberapa detik"
          );
          // Wait a moment before logging out to show the toast
          setTimeout(() => {
            signOut({ callbackUrl: "/login" });
          }, 2000);
        } else {
          toast.success("Berhasil logout dari semua perangkat lain");
          // Refresh the device list
          const sessions = await getDeviceSessions();
          setDevices(
            sessions.map((session) => ({ ...session, isLoggingOut: false }))
          );
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error logging out all devices:", error);
      toast.error("Terjadi kesalahan saat logout dari semua perangkat");
    } finally {
      setIsLoggingOutAll(false);
    }
  };

  // Check for changes in session settings
  useEffect(() => {
    const hasSessionChanges =
      sessionTimeout !== originalSettings.sessionTimeout ||
      loginNotifications !== originalSettings.loginNotifications;

    setHasChanges(hasSessionChanges);
  }, [sessionTimeout, loginNotifications, originalSettings]);

  // Handle session timeout change
  const handleSessionTimeoutChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSessionTimeout(e.target.value);
  };

  // Handle login notifications toggle
  const handleLoginNotificationsChange = (checked: boolean) => {
    setLoginNotifications(checked);
  };

  // Save session settings
  const handleSaveSettings = async () => {
    if (!hasChanges) {
      toast.info("Tidak ada perubahan untuk disimpan");
      return;
    }

    setIsLoading(true);

    try {
      // Save session settings to the database
      const result = await updateSessionSettings({
        sessionTimeout,
        loginNotifications,
      });

      if (result.success) {
        // Update original settings to match current settings
        setOriginalSettings({
          sessionTimeout,
          loginNotifications,
        });

        // Show success message
        toast.success(
          result.message || "Pengaturan keamanan berhasil disimpan!"
        );
        setHasChanges(false);
      } else {
        toast.error(result.message || "Gagal menyimpan pengaturan");
      }
    } catch (error) {
      console.error("Error saving session settings:", error);
      toast.error("Terjadi kesalahan saat menyimpan pengaturan");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Card className="border-none shadow-sm bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader className="pb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <ShieldCheck className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Pengaturan Keamanan</h2>
                <p className="text-sm text-muted-foreground">
                  Kelola pengaturan keamanan dan autentikasi akun Anda
                </p>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="p-6 space-y-8">
        {/* Password Management */}
        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                  <Key className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg">
                    {userPasswordStatus?.hasPassword
                      ? "Ubah Password"
                      : "Buat Password"}
                  </CardTitle>
                  <CardDescription>
                    {userPasswordStatus?.isGoogleUser &&
                    !userPasswordStatus?.hasPassword
                      ? "Buat password untuk akun Google Anda"
                      : userPasswordStatus?.hasPassword
                        ? "Perbarui password akun Anda"
                        : "Buat password untuk akun Anda"}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-6 pt-0">
              {isCheckingPasswordStatus ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  <span className="ml-2 text-sm text-muted-foreground">
                    Memeriksa status password...
                  </span>
                </div>
              ) : (
                <form onSubmit={handlePasswordSubmit}>
                  {/* Info message for Google OAuth users */}
                  {userPasswordStatus?.isGoogleUser &&
                    !userPasswordStatus?.hasPassword && (
                      <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 border border-blue-100 dark:border-blue-800/30 shadow-sm">
                        <div className="flex items-center">
                          <Info className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                          <p className="ml-3 text-sm font-medium text-blue-800 dark:text-blue-300">
                            Anda login menggunakan Google. Buat password untuk
                            dapat login dengan email dan password.
                          </p>
                        </div>
                      </div>
                    )}

                  {passwordSuccess && (
                    <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-4 border border-green-100 dark:border-green-800/30 shadow-sm animate-fade-in">
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                        <p className="ml-3 text-sm font-medium text-green-800 dark:text-green-300">
                          {passwordSuccess}
                        </p>
                      </div>
                    </div>
                  )}

                  {passwordError && (
                    <div className="rounded-lg bg-red-50 dark:bg-red-900/20 p-4 border border-red-100 dark:border-red-800/30 shadow-sm animate-fade-in">
                      <div className="flex items-center">
                        <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                        <p className="ml-3 text-sm font-medium text-red-800 dark:text-red-300">
                          {passwordError}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Conditionally show current password field */}
                    {userPasswordStatus?.needsCurrentPassword && (
                      <div className="space-y-2 sm:col-span-2">
                        <label
                          htmlFor="current-password"
                          className="block text-sm font-medium"
                        >
                          Password Saat Ini
                        </label>
                        <div className="relative">
                          <input
                            type={showCurrentPassword ? "text" : "password"}
                            name="current-password"
                            id="current-password"
                            value={currentPassword}
                            onChange={(e) => setCurrentPassword(e.target.value)}
                            className="w-full px-4 py-2.5 rounded-md border bg-background hover:bg-accent/50 focus-visible:ring-2 focus-visible:ring-ring focus-visible:outline-none pr-10"
                            placeholder="Masukkan password saat ini"
                            required
                            autoComplete="current-password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowCurrentPassword((prev) => !prev)
                            }
                            className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none cursor-pointer"
                          >
                            {showCurrentPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            )}
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Show disabled field with explanation for Google OAuth users */}
                    {userPasswordStatus?.isGoogleUser &&
                      !userPasswordStatus?.hasPassword && (
                        <div className="space-y-2 sm:col-span-2">
                          <label
                            htmlFor="current-password-disabled"
                            className="block text-sm font-medium text-muted-foreground"
                          >
                            Password Saat Ini
                          </label>
                          <div className="relative">
                            <input
                              type="password"
                              name="current-password-disabled"
                              id="current-password-disabled"
                              value="Tidak diperlukan untuk akun Google"
                              disabled
                              className="w-full px-4 py-2.5 rounded-md border bg-muted text-muted-foreground cursor-not-allowed"
                              placeholder="Tidak diperlukan untuk akun Google"
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Akun Google tidak memerlukan password saat ini
                          </p>
                        </div>
                      )}

                    <div className="space-y-2">
                      <label
                        htmlFor="new-password"
                        className="block text-sm font-medium"
                      >
                        Password Baru
                      </label>
                      <div className="relative">
                        <div className="relative">
                          <input
                            type={showNewPassword ? "text" : "password"}
                            name="new-password"
                            id="new-password"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            className="w-full px-4 py-2.5 rounded-md border bg-background hover:bg-accent/50 focus-visible:ring-2 focus-visible:ring-ring focus-visible:outline-none pr-10"
                            placeholder="Masukkan password baru"
                            required
                            autoComplete="new-password"
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword((prev) => !prev)}
                            className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none cursor-pointer"
                          >
                            {showNewPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label
                        htmlFor="confirm-password"
                        className="block text-sm font-medium"
                      >
                        Konfirmasi Password Baru
                      </label>
                      <div className="relative">
                        <div className="relative">
                          <input
                            type={showConfirmPassword ? "text" : "password"}
                            name="confirm-password"
                            id="confirm-password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className="w-full px-4 py-2.5 rounded-md border bg-background hover:bg-accent/50 focus-visible:ring-2 focus-visible:ring-ring focus-visible:outline-none pr-10"
                            placeholder="Konfirmasi password baru"
                            required
                            autoComplete="new-password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowConfirmPassword((prev) => !prev)
                            }
                            className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none cursor-pointer"
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Password Strength Indicator */}
                    {newPassword && (
                      <div className="sm:col-span-2 space-y-2 mt-2 bg-muted/30 p-4 rounded-lg border">
                        <div className="flex justify-between items-center">
                          <label className="text-sm font-medium">
                            Kekuatan Password:
                          </label>
                          <span
                            className={`text-xs font-medium px-2 py-1 rounded-full ${passwordStrength < 50 ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" : passwordStrength < 75 ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300" : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"}`}
                          >
                            {passwordStrength < 50 && "Lemah"}
                            {passwordStrength >= 50 &&
                              passwordStrength < 75 &&
                              "Sedang"}
                            {passwordStrength >= 75 && "Kuat"}
                          </span>
                        </div>
                        <Progress
                          value={passwordStrength}
                          className={`h-2 ${passwordStrength < 50 ? "bg-red-500" : passwordStrength < 75 ? "bg-yellow-500" : "bg-green-500"}`}
                        />

                        <div className="grid grid-cols-2 gap-2 mt-3">
                          <div className="flex items-center gap-2">
                            <div
                              className={`w-4 h-4 rounded-full ${newPassword.length >= 8 ? "bg-green-500" : "bg-muted"}`}
                            ></div>
                            <span className="text-xs text-muted-foreground">
                              Minimal 8 karakter
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div
                              className={`w-4 h-4 rounded-full ${/[A-Z]/.test(newPassword) ? "bg-green-500" : "bg-muted"}`}
                            ></div>
                            <span className="text-xs text-muted-foreground">
                              Huruf besar
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div
                              className={`w-4 h-4 rounded-full ${/[0-9]/.test(newPassword) ? "bg-green-500" : "bg-muted"}`}
                            ></div>
                            <span className="text-xs text-muted-foreground">
                              Angka
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div
                              className={`w-4 h-4 rounded-full ${/[^A-Za-z0-9]/.test(newPassword) ? "bg-green-500" : "bg-muted"}`}
                            ></div>
                            <span className="text-xs text-muted-foreground">
                              Karakter khusus
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end gap-4 pt-4 border-t border-border mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleResetPassword}
                      className="cursor-pointer"
                    >
                      Reset
                    </Button>
                    <Button
                      type="submit"
                      disabled={isPasswordLoading}
                      className="cursor-pointer"
                    >
                      {isPasswordLoading
                        ? userPasswordStatus?.hasPassword
                          ? "Memperbarui..."
                          : "Membuat..."
                        : userPasswordStatus?.hasPassword
                          ? "Perbarui Password"
                          : "Buat Password"}
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>
          </Card>
        </div>
        {/* Two-Factor Authentication */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                <ShieldCheck className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">
                  Autentikasi Dua Faktor
                </CardTitle>
                <CardDescription>
                  Tingkatkan keamanan akun Anda dengan autentikasi dua faktor
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
              <div>
                <h4 className="text-sm font-medium">
                  Aktifkan Autentikasi Dua Faktor
                </h4>
                <p className="text-xs text-muted-foreground mt-1">
                  Memerlukan kode verifikasi tambahan saat login
                </p>
              </div>
              <Switch
                checked={twoFactorEnabled}
                onCheckedChange={() => setTwoFactorEnabled(!twoFactorEnabled)}
                className="cursor-pointer"
              />
            </div>

            {twoFactorEnabled && (
              <div className="mt-4 p-4 bg-primary/5 rounded-lg border border-primary/20">
                <h4 className="text-sm font-medium text-primary">
                  Pengaturan Autentikasi Dua Faktor
                </h4>
                <p className="text-xs text-muted-foreground mt-1 mb-4">
                  Fitur ini akan segera tersedia. Anda akan dapat menggunakan
                  aplikasi autentikator atau SMS untuk verifikasi.
                </p>
                <Button
                  variant="default"
                  size="sm"
                  className="cursor-pointer"
                  disabled
                >
                  Konfigurasi 2FA
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Session Settings */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                <Clock className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">Pengaturan Sesi</CardTitle>
                <CardDescription>
                  Kelola durasi dan perilaku sesi login Anda
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            {isLoadingSettings ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                <p className="text-sm text-muted-foreground">
                  Memuat pengaturan...
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <label
                    htmlFor="session-timeout"
                    className="text-sm font-medium"
                  >
                    Batas Waktu Sesi (menit)
                  </label>
                  <select
                    id="session-timeout"
                    value={sessionTimeout}
                    onChange={handleSessionTimeoutChange}
                    className="w-full px-4 py-2.5 rounded-md border bg-background hover:bg-accent/50 focus-visible:ring-2 focus-visible:ring-ring"
                  >
                    <option value="15">15 menit</option>
                    <option value="30">30 menit</option>
                    <option value="60">1 jam</option>
                    <option value="120">2 jam</option>
                    <option value="240">4 jam</option>
                    <option value="480">8 jam</option>
                    <option value="1440">24 jam</option>
                  </select>
                  <p className="text-xs text-muted-foreground">
                    Sesi Anda akan berakhir secara otomatis setelah tidak aktif
                    selama periode ini
                  </p>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
                  <div>
                    <h4 className="text-sm font-medium">Notifikasi Login</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      Kirim notifikasi email saat ada login baru ke akun Anda
                    </p>
                  </div>
                  <Switch
                    checked={loginNotifications}
                    onCheckedChange={handleLoginNotificationsChange}
                    className="cursor-pointer"
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Device Management */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                <Laptop className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">Manajemen Perangkat</CardTitle>
                <CardDescription>
                  Lihat dan kelola perangkat yang terhubung ke akun Anda
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            <div className="space-y-4">
              {isLoadingDevices ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                  <p className="text-sm text-muted-foreground">
                    Memuat data perangkat...
                  </p>
                </div>
              ) : devices.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-4">
                    <Info className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h4 className="text-sm font-medium mb-1">
                    Tidak ada perangkat aktif
                  </h4>
                  <p className="text-xs text-muted-foreground max-w-xs">
                    Tidak ada perangkat lain yang login ke akun Anda saat ini
                  </p>
                </div>
              ) : (
                <>
                  {devices.map((device) => (
                    <div
                      key={device.id}
                      className="p-4 bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`h-8 w-8 rounded-full ${device.type === "mobile" ? "bg-purple-100 dark:bg-purple-900/20" : device.type === "tablet" ? "bg-amber-100 dark:bg-amber-900/20" : "bg-blue-100 dark:bg-blue-900/20"} flex items-center justify-center`}
                          >
                            {device.type === "mobile" ? (
                              <Smartphone className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                            ) : device.type === "tablet" ? (
                              <Tablet className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                            ) : (
                              <Laptop className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                            )}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="text-sm font-medium">
                                {device.name}
                              </h4>
                              {device.isCurrent && (
                                <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                                  Saat ini
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                              {device.isCurrent
                                ? `Perangkat ini • Login terakhir: ${device.lastLogin}`
                                : `Login terakhir: ${device.lastLogin}`}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={
                            device.isLoggingOut ||
                            (device.isCurrent && isLoggingOutAll)
                          }
                          className="text-destructive border-destructive/30 hover:bg-destructive/10 cursor-pointer"
                          onClick={() => handleLogoutDevice(device.id)}
                        >
                          {device.isLoggingOut ? (
                            <>
                              <span className="h-3.5 w-3.5 mr-1 animate-spin rounded-full border-2 border-current border-t-transparent" />
                              Proses...
                            </>
                          ) : (
                            <>
                              <LogOut className="h-3.5 w-3.5 mr-1" />
                              Logout
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}

                  <Button
                    variant="outline"
                    className="w-full mt-2 border-primary/30 text-primary hover:bg-primary/10 cursor-pointer"
                    disabled={isLoggingOutAll || devices.length === 0}
                    onClick={handleLogoutAllDevices}
                  >
                    {isLoggingOutAll ? (
                      <>
                        <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        Proses...
                      </>
                    ) : (
                      <>
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout dari Semua Perangkat
                      </>
                    )}
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="pt-6 flex justify-end">
          <Button
            type="button"
            onClick={handleSaveSettings}
            disabled={isLoading || !hasChanges}
            className={`cursor-pointer ${!hasChanges && !isLoading ? "opacity-70" : ""}`}
          >
            {isLoading ? "Menyimpan..." : "Simpan Perubahan"}
          </Button>
        </div>
      </div>

      {/* Custom Animation Styles */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </div>
  );
}
