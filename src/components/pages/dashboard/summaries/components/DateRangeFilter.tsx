"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon } from "@heroicons/react/24/outline";

export type DateRange = "7days" | "30days" | "90days" | "year" | "custom";

interface DateRangeFilterProps {
  selectedRange: DateRange;
  onRangeChange: (range: DateRange) => void;
  className?: string;
}

export function DateRangeFilter({
  selectedRange,
  onRangeChange,
  className = "",
}: DateRangeFilterProps) {
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className="flex items-center">
        <CalendarIcon className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
          Periode:
        </span>
      </div>
      <Select
        value={selectedRange}
        onValueChange={(value) => onRangeChange(value as DateRange)}
      >
        <SelectTrigger className="h-9 w-[180px] border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors">
          <SelectValue placeholder="Pilih periode" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="7days">7 Hari Terakhir</SelectItem>
          <SelectItem value="30days">30 Hari Terakhir</SelectItem>
          <SelectItem value="90days">90 Hari Terakhir</SelectItem>
          <SelectItem value="year">Tahun Ini</SelectItem>
          <SelectItem value="custom">Kustom</SelectItem>
        </SelectContent>
      </Select>
      <Button variant="outline" size="sm" className="h-8">
        Terapkan
      </Button>
    </div>
  );
}
