"use client";

import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  ClockIcon,
  CubeIcon,
  ShoppingBagIcon,
  BanknotesIcon,
  ArrowTopRightOnSquareIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

// Legacy interface for backward compatibility
export interface RecentTransactionItem {
  id: string;
  time: string;
  amount: string;
  status?: "success" | "pending" | "failed";
}

// Enhanced activity interface
export interface RecentActivityItem {
  id: string;
  type: "product" | "purchase" | "sale";
  title: string;
  description: string;
  time: string;
  amount?: string;
  icon: string;
  color: string;
  href?: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface RecentActivityProps {
  activities: RecentActivityItem[];
  pagination?: PaginationInfo;
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
  className?: string;
}

// Helper function to get icon component
const getActivityIcon = (iconName: string, className: string = "h-5 w-5") => {
  switch (iconName) {
    case "cube":
      return <CubeIcon className={className} />;
    case "shopping-bag":
      return <ShoppingBagIcon className={className} />;
    case "banknotes":
      return <BanknotesIcon className={className} />;
    default:
      return <ClockIcon className={className} />;
  }
};

// Helper function to get color classes
const getColorClasses = (color: string) => {
  switch (color) {
    case "amber":
      return {
        bg: "bg-amber-100 dark:bg-amber-900/30",
        text: "text-amber-600 dark:text-amber-400",
        border: "border-amber-200 dark:border-amber-800",
      };
    case "emerald":
      return {
        bg: "bg-emerald-100 dark:bg-emerald-900/30",
        text: "text-emerald-600 dark:text-emerald-400",
        border: "border-emerald-200 dark:border-emerald-800",
      };
    case "indigo":
      return {
        bg: "bg-indigo-100 dark:bg-indigo-900/30",
        text: "text-indigo-600 dark:text-indigo-400",
        border: "border-indigo-200 dark:border-indigo-800",
      };
    default:
      return {
        bg: "bg-gray-100 dark:bg-gray-900/30",
        text: "text-gray-600 dark:text-gray-400",
        border: "border-gray-200 dark:border-gray-800",
      };
  }
};

export function RecentActivity({
  activities,
  pagination,
  onPageChange,
  isLoading = false,
  className = "",
}: RecentActivityProps) {
  return (
    <Card
      className={`border-none shadow-md hover:shadow-lg transition-all duration-300 dark:bg-gray-800 group ${className}`}
    >
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <ClockIcon className="mr-2 h-5 w-5 text-gray-500" />
          Aktivitas Terbaru
        </CardTitle>
        <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
          Aktivitas terbaru dari produk, pembelian, dan penjualan
        </CardDescription>
      </CardHeader>

      <CardContent className="p-0">
        <div className="max-h-96 overflow-y-auto">
          {isLoading ? (
            // Loading skeleton
            <div className="divide-y dark:divide-gray-700">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="px-6 py-4 animate-pulse">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-2 rounded-lg bg-gray-200 dark:bg-gray-700 w-8 h-8"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 w-3/4"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2 w-1/2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : activities.length > 0 ? (
            <div className="divide-y dark:divide-gray-700">
              {activities.map((activity) => {
                const colors = getColorClasses(activity.color);
                const ActivityComponent = activity.href ? Link : "div";

                return (
                  <ActivityComponent
                    key={activity.id}
                    href={activity.href || "#"}
                    className={`block px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 ${
                      activity.href ? "cursor-pointer" : ""
                    }`}
                  >
                    <div className="flex items-start space-x-4">
                      {/* Activity Icon */}
                      <div
                        className={`flex-shrink-0 p-2 rounded-lg ${colors.bg} ${colors.border} border`}
                      >
                        {getActivityIcon(
                          activity.icon,
                          `h-4 w-4 ${colors.text}`
                        )}
                      </div>

                      {/* Activity Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {activity.title}
                          </p>
                          {activity.href && (
                            <ArrowTopRightOnSquareIcon className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                          )}
                        </div>

                        <p className="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
                          {activity.description}
                        </p>

                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center text-xs text-gray-400 dark:text-gray-500">
                            <ClockIcon className="mr-1 h-3 w-3" />
                            {activity.time}
                          </div>

                          {activity.amount && (
                            <span className="text-sm font-semibold text-gray-900 dark:text-white">
                              {activity.amount}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </ActivityComponent>
                );
              })}
            </div>
          ) : (
            <div className="px-6 py-12 text-center">
              <div className="text-gray-400 dark:text-gray-500">
                <ClockIcon className="mx-auto h-12 w-12 mb-4" />
                <p className="text-sm font-medium">
                  Tidak ada aktivitas terbaru
                </p>
                <p className="text-xs mt-1">
                  Aktivitas akan muncul setelah Anda menambah produk, melakukan
                  pembelian, atau penjualan
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Pagination Controls - Always show when pagination data exists */}
        {pagination && (
          <div className="border-t dark:border-gray-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Halaman {pagination.currentPage} dari {pagination.totalPages} (
                {pagination.totalCount} total aktivitas)
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage || isLoading}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                </Button>

                <span className="text-sm font-medium text-gray-900 dark:text-white px-2">
                  {pagination.currentPage}
                </span>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage || isLoading}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {activities.length > 0 && (
        <CardFooter className="bg-gray-50 dark:bg-gray-700/50 border-t dark:border-gray-700 p-4">
          <div className="grid grid-cols-3 gap-2 w-full">
            <Button asChild variant="outline" size="sm" className="text-xs">
              <Link href="/dashboard/products">Produk</Link>
            </Button>
            <Button asChild variant="outline" size="sm" className="text-xs">
              <Link href="/dashboard/purchases">Pembelian</Link>
            </Button>
            <Button asChild variant="outline" size="sm" className="text-xs">
              <Link href="/dashboard/sales">Penjualan</Link>
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
