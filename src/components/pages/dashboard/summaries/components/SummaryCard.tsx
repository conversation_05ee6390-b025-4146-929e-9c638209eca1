"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";

export type ColorScheme =
  | "indigo"
  | "emerald"
  | "blue"
  | "amber"
  | "rose"
  | "purple";

interface SummaryCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  change?: {
    value: string;
    type: "increase" | "decrease";
  };
  linkText?: string;
  linkHref?: string;
  colorScheme?: ColorScheme;
  className?: string;
}

export function SummaryCard({
  title,
  value,
  icon,
  change,
  linkText,
  linkHref,
  colorScheme = "indigo",
  className = "",
}: SummaryCardProps) {
  // Enhanced color schemes with gradients and modern styling
  const colorSchemes = {
    indigo: {
      cardBg:
        "bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950 dark:to-indigo-900",
      iconBg: "bg-gradient-to-br from-indigo-500 to-indigo-600",
      iconText: "text-white",
      valueText: "text-indigo-900 dark:text-indigo-100",
      titleText: "text-indigo-700 dark:text-indigo-300",
      changeText: "text-indigo-600 dark:text-indigo-400",
      linkText:
        "text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300",
      border: "border-indigo-200 dark:border-indigo-800",
    },
    emerald: {
      cardBg:
        "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900",
      iconBg: "bg-gradient-to-br from-emerald-500 to-emerald-600",
      iconText: "text-white",
      valueText: "text-emerald-900 dark:text-emerald-100",
      titleText: "text-emerald-700 dark:text-emerald-300",
      changeText: "text-emerald-600 dark:text-emerald-400",
      linkText:
        "text-emerald-600 hover:text-emerald-800 dark:text-emerald-400 dark:hover:text-emerald-300",
      border: "border-emerald-200 dark:border-emerald-800",
    },
    blue: {
      cardBg:
        "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900",
      iconBg: "bg-gradient-to-br from-blue-500 to-blue-600",
      iconText: "text-white",
      valueText: "text-blue-900 dark:text-blue-100",
      titleText: "text-blue-700 dark:text-blue-300",
      changeText: "text-blue-600 dark:text-blue-400",
      linkText:
        "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",
      border: "border-blue-200 dark:border-blue-800",
    },
    amber: {
      cardBg:
        "bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950 dark:to-amber-900",
      iconBg: "bg-gradient-to-br from-amber-500 to-amber-600",
      iconText: "text-white",
      valueText: "text-amber-900 dark:text-amber-100",
      titleText: "text-amber-700 dark:text-amber-300",
      changeText: "text-amber-600 dark:text-amber-400",
      linkText:
        "text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300",
      border: "border-amber-200 dark:border-amber-800",
    },
    rose: {
      cardBg:
        "bg-gradient-to-br from-rose-50 to-rose-100 dark:from-rose-950 dark:to-rose-900",
      iconBg: "bg-gradient-to-br from-rose-500 to-rose-600",
      iconText: "text-white",
      valueText: "text-rose-900 dark:text-rose-100",
      titleText: "text-rose-700 dark:text-rose-300",
      changeText: "text-rose-600 dark:text-rose-400",
      linkText:
        "text-rose-600 hover:text-rose-800 dark:text-rose-400 dark:hover:text-rose-300",
      border: "border-rose-200 dark:border-rose-800",
    },
    purple: {
      cardBg:
        "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900",
      iconBg: "bg-gradient-to-br from-purple-500 to-purple-600",
      iconText: "text-white",
      valueText: "text-purple-900 dark:text-purple-100",
      titleText: "text-purple-700 dark:text-purple-300",
      changeText: "text-purple-600 dark:text-purple-400",
      linkText:
        "text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300",
      border: "border-purple-200 dark:border-purple-800",
    },
  };

  const colors = colorSchemes[colorScheme];

  const cardContent = (
    <Card
      className={`relative overflow-hidden border ${colors.border} ${colors.cardBg} shadow-lg hover:shadow-xl transition-all duration-300 ${linkHref ? "hover:scale-[1.02] cursor-pointer hover:-translate-y-1" : ""} ${className}`}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-white/20"></div>
        <div className="absolute -bottom-6 -left-6 h-32 w-32 rounded-full bg-white/10"></div>
      </div>

      <CardContent className="relative p-4">
        {/* Header with Icon */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className={`text-sm font-semibold ${colors.titleText}`}>
              {title}
            </h3>
          </div>
          <div
            className={`flex-shrink-0 rounded-lg ${colors.iconBg} p-2 ${colors.iconText} shadow-lg`}
          >
            <div className="h-5 w-5 flex items-center justify-center">
              {icon}
            </div>
          </div>
        </div>

        {/* Value */}
        <div className={`text-2xl font-bold ${colors.valueText} mb-2`}>
          {value}
        </div>

        {/* Change Indicator */}
        {change && (
          <div className="flex items-center space-x-1 mb-2">
            <div
              className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                change.type === "increase"
                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                  : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
              }`}
            >
              {change.type === "increase" ? (
                <ArrowUpIcon className="h-3 w-3 mr-1" />
              ) : (
                <ArrowDownIcon className="h-3 w-3 mr-1" />
              )}
              {change.value}
            </div>
          </div>
        )}

        {/* Link Text */}
        {linkText && linkHref && (
          <div className="pt-2 border-t border-white/20">
            <span
              className={`text-xs font-medium ${colors.linkText} flex items-center group`}
            >
              {linkText}
              <svg
                className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (linkHref) {
    return (
      <Link href={linkHref} className="block">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
}
