"use client";

import React, { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Terminal } from "lucide-react";
import {
  getDashboardSummary,
  getSalesChartData,
  getPurchaseTrendData,
  getProductDistributionData,
  getRecentTransactions,
  getRecentActivities,
  type RecentActivityItem,
} from "@/actions/dashboard/dashboard";

// Import our custom components
import { SummaryCardsSection } from "./components/SummaryCardsSection";
import { SalesBarChart } from "./components/SalesBarChart";
import { PurchaseTrendBarChart } from "./components/PurchaseTrendBarChart";
import { ProductDistributionBarChart } from "./components/ProductDistributionBarChart";
import { RecentActivity } from "./components/RecentActivity";
import { DateRangeFilter, DateRange } from "./components/DateRangeFilter";
import { QuickActions } from "@/components/dashboard/quick-actions";
import { PlusIcon, CubeIcon } from "@heroicons/react/24/outline";

// Define types for data
type SummaryData = {
  salesToday: number;
  salesChange: number;
  totalProducts: number;
  newProductsCount: number;
  totalCustomers: number;
  newCustomersCount: number;
  purchasesThisMonth: number;
  purchasesChange: number;
};

type SalesChartDataPoint = {
  name: string;
  total: number;
};

type PurchaseTrendDataPoint = {
  name: string;
  total: number;
};

type ProductDistributionDataPoint = {
  name: string;
  value: number;
};

type RecentTransactionItem = {
  id: string;
  time: string;
  amount: string;
  status?: "success" | "pending" | "failed";
};

export default function SummariesPage() {
  // State for all dashboard data
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [salesChartData, setSalesChartData] = useState<
    SalesChartDataPoint[] | null
  >(null);
  const [purchaseTrendData, setPurchaseTrendData] = useState<
    PurchaseTrendDataPoint[] | null
  >(null);
  const [productDistData, setProductDistData] = useState<
    ProductDistributionDataPoint[] | null
  >(null);
  const [recentTransData, setRecentTransData] = useState<
    RecentTransactionItem[] | null
  >(null);
  const [recentActivities, setRecentActivities] = useState<
    RecentActivityItem[] | null
  >(null);
  const [activitiesPagination, setActivitiesPagination] = useState<{
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  } | null>(null);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  const [currentActivitiesPage, setCurrentActivitiesPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange>("30days");

  // Function to load activities with pagination
  const loadActivities = async (page: number = 1) => {
    setActivitiesLoading(true);
    try {
      const result = await getRecentActivities(page, 6); // 6 items per page

      if (result.success && result.data) {
        setRecentActivities(result.data);
        setActivitiesPagination(result.pagination || null);
        setCurrentActivitiesPage(page);
      } else {
        console.error("Failed to fetch activities:", result.error);
        setRecentActivities([]);
        setActivitiesPagination(null);
      }
    } catch (error) {
      console.error("Error loading activities:", error);
      setRecentActivities([]);
      setActivitiesPagination(null);
    } finally {
      setActivitiesLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    loadActivities(page);
  };

  // Quick actions for the summaries page
  const quickActions = [
    {
      label: "Kelola Produk",
      href: "/dashboard/products",
      icon: <CubeIcon className="mr-2 h-4 w-4" />,
      colorClass: "bg-amber-600 hover:bg-amber-700",
    },
    {
      label: "Tambah Pembelian",
      href: "/dashboard/purchases/new",
      icon: <PlusIcon className="mr-2 h-4 w-4" />,
      colorClass: "bg-emerald-600 hover:bg-emerald-700",
    },
    {
      label: "Tambah Penjualan",
      href: "/dashboard/sales/new",
      icon: <PlusIcon className="mr-2 h-4 w-4" />,
      colorClass: "bg-indigo-600 hover:bg-indigo-700",
    },
  ];

  useEffect(() => {
    const fetchAllData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch all data concurrently
        const [
          summaryResult,
          salesChartResult,
          purchaseTrendResult,
          productDistResult,
          recentTransResult,
          recentActivitiesResult,
        ] = await Promise.all([
          getDashboardSummary(),
          getSalesChartData(),
          getPurchaseTrendData(), // Use 6 months for trend data
          getProductDistributionData(),
          getRecentTransactions(),
          getRecentActivities(1, 6), // Load first page with 6 items
        ]);

        // Process results and update state - don't throw errors, just log them
        if (summaryResult.success && summaryResult.data) {
          setSummaryData(summaryResult.data);
        } else {
          console.error("Failed to fetch summary data:", summaryResult.error);
          setSummaryData({
            salesToday: 0,
            salesChange: 0,
            totalProducts: 0,
            newProductsCount: 0,
            totalCustomers: 0,
            newCustomersCount: 0,
            purchasesThisMonth: 0,
            purchasesChange: 0,
          });
        }

        if (salesChartResult.success && salesChartResult.data) {
          setSalesChartData(salesChartResult.data);
        } else {
          console.error(
            "Failed to fetch sales chart data:",
            salesChartResult.error
          );
          setSalesChartData([]);
        }

        if (purchaseTrendResult.success && purchaseTrendResult.data) {
          // getPurchaseTrendData returns data directly as array
          setPurchaseTrendData(purchaseTrendResult.data);
        } else {
          console.error(
            "Failed to fetch purchase trend data:",
            purchaseTrendResult.error
          );
          setPurchaseTrendData([]);
        }

        if (productDistResult.success && productDistResult.data) {
          setProductDistData(productDistResult.data);
        } else {
          console.error(
            "Failed to fetch product distribution data:",
            productDistResult.error
          );
          setProductDistData([]);
        }

        if (recentTransResult.success && recentTransResult.data) {
          setRecentTransData(recentTransResult.data);
        } else {
          console.error(
            "Failed to fetch recent transactions data:",
            recentTransResult.error
          );
          setRecentTransData([]);
        }

        if (recentActivitiesResult.success && recentActivitiesResult.data) {
          setRecentActivities(recentActivitiesResult.data);
          setActivitiesPagination(recentActivitiesResult.pagination || null);
        } else {
          console.error(
            "Failed to fetch recent activities data:",
            recentActivitiesResult.error
          );
          setRecentActivities([]);
          setActivitiesPagination(null);
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Terjadi kesalahan saat mengambil data dashboard."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllData();
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="border-none shadow-md dark:bg-gray-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-28 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card
              key={i}
              className="col-span-1 border-none shadow-md dark:bg-gray-800"
            >
              <CardHeader>
                <Skeleton className="h-5 w-32 mb-2" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[320px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    // Special handling for authentication errors
    if (error.includes("Tidak terautentikasi")) {
      return (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Akses Ditolak</AlertTitle>
          <AlertDescription>
            Anda perlu login untuk melihat data dashboard. Silakan login
            kembali.
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // Note: Removed strict data validation - components will handle empty data gracefully

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Ringkasan Bisnis
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Pantau performa bisnis Anda dalam satu tampilan
          </p>
        </div>
        <DateRangeFilter
          selectedRange={dateRange}
          onRangeChange={setDateRange}
        />
      </div>

      {/* Quick Actions */}
      <QuickActions actions={quickActions} />

      {/* Summary Cards */}
      {summaryData && <SummaryCardsSection summaryData={summaryData} />}

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Purchase Trend Chart */}
        <div className="col-span-1">
          <PurchaseTrendBarChart
            data={purchaseTrendData || []}
            title="Tren Pembelian"
            description="Pembelian 6 bulan terakhir"
          />
        </div>

        {/* Sales Chart */}
        <div className="col-span-1">
          <SalesBarChart
            data={salesChartData || []}
            title="Tren Penjualan"
            description="Penjualan 6 bulan terakhir"
          />
        </div>

        {/* Product Distribution Chart */}
        <div className="col-span-1">
          <ProductDistributionBarChart
            data={productDistData || []}
            title="Distribusi Produk"
            description="Berdasarkan kategori"
          />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-6">
        <RecentActivity
          activities={recentActivities || []}
          pagination={activitiesPagination || undefined}
          onPageChange={handlePageChange}
          isLoading={activitiesLoading}
        />
      </div>
    </div>
  );
}
