"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  RotateCcw,
} from "lucide-react";
import { Warehouse, StockMovement, StockMovementType } from "@/types/warehouse";
import { getWarehouses } from "@/actions/entities/warehouses";
import { getStockMovements } from "@/actions/entities/warehouse-stock";

export const StockMovementHistory: React.FC = () => {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<string>("all");
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Load warehouses
  const loadWarehouses = async () => {
    try {
      const data = await getWarehouses();
      setWarehouses(data.filter((w) => w.isActive));
    } catch (error) {
      console.error("Error loading warehouses:", error);
      toast.error("Gagal memuat data gudang");
    }
  };

  // Load stock movements
  const loadMovements = async () => {
    try {
      setLoading(true);
      const warehouseId =
        selectedWarehouseId === "all" ? undefined : selectedWarehouseId;
      const data = await getStockMovements(warehouseId, undefined, 100);
      setMovements(data);
    } catch (error) {
      console.error("Error loading movements:", error);
      toast.error("Gagal memuat riwayat pergerakan stok");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWarehouses();
  }, []);

  useEffect(() => {
    loadMovements();
  }, [selectedWarehouseId]);

  // Filter movements based on search term
  const filteredMovements = movements.filter(
    (movement) =>
      movement.product?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (movement.product?.sku &&
        movement.product.sku
          .toLowerCase()
          .includes(searchTerm.toLowerCase())) ||
      movement.warehouse?.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (movement.notes &&
        movement.notes.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getMovementTypeInfo = (type: StockMovementType) => {
    switch (type) {
      case StockMovementType.PURCHASE:
        return {
          label: "Pembelian",
          icon: TrendingUp,
          color: "text-green-600",
          bgColor: "bg-green-50",
          variant: "default" as const,
        };
      case StockMovementType.SALE:
        return {
          label: "Penjualan",
          icon: TrendingDown,
          color: "text-red-600",
          bgColor: "bg-red-50",
          variant: "destructive" as const,
        };
      case StockMovementType.TRANSFER_IN:
        return {
          label: "Transfer Masuk",
          icon: TrendingUp,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
          variant: "default" as const,
        };
      case StockMovementType.TRANSFER_OUT:
        return {
          label: "Transfer Keluar",
          icon: TrendingDown,
          color: "text-orange-600",
          bgColor: "bg-orange-50",
          variant: "secondary" as const,
        };
      case StockMovementType.ADJUSTMENT:
        return {
          label: "Penyesuaian",
          icon: RotateCcw,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
          variant: "outline" as const,
        };
      case StockMovementType.RETURN:
        return {
          label: "Retur",
          icon: TrendingUp,
          color: "text-indigo-600",
          bgColor: "bg-indigo-50",
          variant: "default" as const,
        };
      case StockMovementType.DAMAGE:
        return {
          label: "Kerusakan",
          icon: TrendingDown,
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          variant: "secondary" as const,
        };
      default:
        return {
          label: "Lainnya",
          icon: RotateCcw,
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          variant: "outline" as const,
        };
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleString("id-ID", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleRefresh = () => {
    loadMovements();
    toast.success("Riwayat pergerakan berhasil diperbarui");
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-4 max-w-2xl">
          <Select
            value={selectedWarehouseId}
            onValueChange={setSelectedWarehouseId}
          >
            <SelectTrigger className="w-[250px]">
              <SelectValue placeholder="Pilih gudang" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Gudang</SelectItem>
              {warehouses.map((warehouse) => (
                <SelectItem key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                  {warehouse.isDefault && " (Default)"}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cari pergerakan..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-11"
            />
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Movement History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Riwayat Pergerakan Stok</span>
            <Badge variant="secondary">
              {filteredMovements.length} pergerakan
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-500">Memuat riwayat pergerakan...</p>
            </div>
          ) : filteredMovements.length === 0 ? (
            <div className="text-center py-8">
              <RotateCcw className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">
                {searchTerm
                  ? "Tidak ada pergerakan yang sesuai dengan pencarian"
                  : "Belum ada riwayat pergerakan stok"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredMovements.map((movement) => {
                const typeInfo = getMovementTypeInfo(movement.type);
                const Icon = typeInfo.icon;

                return (
                  <div
                    key={movement.id}
                    className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50"
                  >
                    <div className={`p-2 rounded-lg ${typeInfo.bgColor}`}>
                      <Icon className={`h-5 w-5 ${typeInfo.color}`} />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate">
                          {movement.product?.name}
                        </h4>
                        <Badge variant={typeInfo.variant} className="text-xs">
                          {typeInfo.label}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>Gudang: {movement.warehouse?.name}</span>
                        {movement.product?.sku && (
                          <span>SKU: {movement.product.sku}</span>
                        )}
                        <span>{formatDate(movement.createdAt)}</span>
                      </div>

                      {movement.notes && (
                        <p className="text-sm text-gray-600 mt-1 truncate">
                          {movement.notes}
                        </p>
                      )}
                    </div>

                    <div className="text-right">
                      <div className="flex items-center gap-2">
                        <span
                          className={`font-semibold ${movement.quantity > 0 ? "text-green-600" : "text-red-600"}`}
                        >
                          {movement.quantity > 0 ? "+" : ""}
                          {movement.quantity}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {movement.previousStock} → {movement.newStock}
                      </div>
                      {movement.reference && (
                        <div className="text-xs text-gray-400 mt-1">
                          Ref: {movement.reference}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}

              {movements.length >= 100 && (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">
                    Menampilkan 100 pergerakan terbaru. Gunakan filter untuk
                    melihat data spesifik.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
