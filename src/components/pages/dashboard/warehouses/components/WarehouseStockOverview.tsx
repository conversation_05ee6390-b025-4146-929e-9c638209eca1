"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, RefreshCw, Package, AlertTriangle } from "lucide-react";
import { Warehouse, WarehouseStock } from "@/types/warehouse";
import { getWarehouses } from "@/actions/entities/warehouses";
import { getWarehouseStock } from "@/actions/entities/warehouse-stock";

export const WarehouseStockOverview: React.FC = () => {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<string>("");
  const [stocks, setStocks] = useState<WarehouseStock[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Load warehouses
  const loadWarehouses = async () => {
    try {
      const data = await getWarehouses();
      setWarehouses(data.filter((w) => w.isActive));

      // Auto-select default warehouse or first warehouse
      const defaultWarehouse = data.find((w) => w.isDefault && w.isActive);
      const firstWarehouse = data.find((w) => w.isActive);
      const selectedId = defaultWarehouse?.id || firstWarehouse?.id || "";

      setSelectedWarehouseId(selectedId);
    } catch (error) {
      console.error("Error loading warehouses:", error);
      toast.error("Gagal memuat data gudang");
    }
  };

  // Load stock for selected warehouse
  const loadStock = async (warehouseId: string) => {
    if (!warehouseId) {
      setStocks([]);
      return;
    }

    try {
      setLoading(true);
      const data = await getWarehouseStock(warehouseId);
      setStocks(data);
    } catch (error) {
      console.error("Error loading stock:", error);
      toast.error("Gagal memuat data stok");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWarehouses();
  }, []);

  useEffect(() => {
    if (selectedWarehouseId) {
      loadStock(selectedWarehouseId);
    } else {
      setLoading(false);
    }
  }, [selectedWarehouseId]);

  // Filter stocks based on search term
  const filteredStocks = stocks.filter(
    (stock) =>
      stock.product?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (stock.product?.sku &&
        stock.product.sku.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const selectedWarehouse = warehouses.find(
    (w) => w.id === selectedWarehouseId
  );

  // Calculate summary stats
  const totalProducts = filteredStocks.length;
  const totalStock = filteredStocks.reduce(
    (sum, stock) => sum + stock.quantity,
    0
  );
  const lowStockItems = filteredStocks.filter(
    (stock) => stock.minLevel && stock.quantity <= stock.minLevel
  ).length;
  const outOfStockItems = filteredStocks.filter(
    (stock) => stock.quantity === 0
  ).length;

  const getStockStatus = (stock: WarehouseStock) => {
    if (stock.quantity === 0) {
      return { label: "Habis", variant: "destructive" as const };
    }
    if (stock.minLevel && stock.quantity <= stock.minLevel) {
      return { label: "Stok Rendah", variant: "secondary" as const };
    }
    return { label: "Normal", variant: "default" as const };
  };

  const handleRefresh = () => {
    if (selectedWarehouseId) {
      loadStock(selectedWarehouseId);
      toast.success("Data stok berhasil diperbarui");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-4 max-w-2xl">
          <Select
            value={selectedWarehouseId}
            onValueChange={setSelectedWarehouseId}
          >
            <SelectTrigger className="w-[250px]">
              <SelectValue placeholder="Pilih gudang" />
            </SelectTrigger>
            <SelectContent>
              {warehouses.map((warehouse) => (
                <SelectItem key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                  {warehouse.isDefault && " (Default)"}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cari produk..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-11"
            />
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={loading || !selectedWarehouseId}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      {selectedWarehouse && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Produk</p>
                  <p className="text-2xl font-bold">{totalProducts}</p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Stok</p>
                  <p className="text-2xl font-bold">{totalStock}</p>
                </div>
                <Package className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Stok Rendah</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {lowStockItems}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Stok Habis</p>
                  <p className="text-2xl font-bold text-red-600">
                    {outOfStockItems}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Stock Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              Stok Produk {selectedWarehouse && `- ${selectedWarehouse.name}`}
            </span>
            <Badge variant="secondary">{filteredStocks.length} produk</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-500">Memuat data stok...</p>
            </div>
          ) : !selectedWarehouseId ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">
                Pilih gudang untuk melihat stok produk
              </p>
            </div>
          ) : filteredStocks.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">
                {searchTerm
                  ? "Tidak ada produk yang sesuai dengan pencarian"
                  : "Belum ada stok produk di gudang ini"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Fitur lengkap manajemen stok akan segera hadir. Saat ini
                menampilkan data stok dari gudang yang dipilih.
              </p>

              {/* Simple stock list */}
              <div className="space-y-2">
                {filteredStocks.slice(0, 10).map((stock) => {
                  const status = getStockStatus(stock);
                  return (
                    <div
                      key={stock.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium">{stock.product?.name}</h4>
                        {stock.product?.sku && (
                          <p className="text-sm text-gray-500">
                            SKU: {stock.product.sku}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-semibold">
                            {stock.quantity} {stock.product?.unit}
                          </p>
                          {stock.minLevel && (
                            <p className="text-xs text-gray-500">
                              Min: {stock.minLevel}
                            </p>
                          )}
                        </div>
                        <Badge variant={status.variant}>{status.label}</Badge>
                      </div>
                    </div>
                  );
                })}

                {filteredStocks.length > 10 && (
                  <p className="text-sm text-gray-500 text-center py-2">
                    Dan {filteredStocks.length - 10} produk lainnya...
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
