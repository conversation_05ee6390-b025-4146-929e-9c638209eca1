"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";

export default function NextAuthProvider({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <SessionProvider
      refetchInterval={15 * 60} // Refetch session every 15 minutes
      refetchOnWindowFocus={false} // Disable automatic refetch on window focus
    >
      {children}
    </SessionProvider>
  );
}
