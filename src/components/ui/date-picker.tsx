"use client";

import * as React from "react";
import { format, getYear, getMonth } from "date-fns";
import { Calendar as CalendarIcon, ChevronDown } from "lucide-react";
import { id } from "date-fns/locale";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  fromYear?: number;
  toYear?: number;
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "Pilih tanggal",
  disabled = false,
  fromYear = 1950,
  toYear = new Date().getFullYear(),
}: DatePickerProps) {
  const [calendarDate, setCalendarDate] = React.useState<Date | undefined>(
    date || new Date()
  );
  const [year, setYear] = React.useState<number>(
    date ? getYear(date) : getYear(new Date())
  );
  const [month, setMonth] = React.useState<number>(
    date ? getMonth(date) : getMonth(new Date())
  );

  // Generate years for dropdown
  const years = React.useMemo(() => {
    const years = [];
    for (let i = toYear; i >= fromYear; i--) {
      years.push(i);
    }
    return years;
  }, [fromYear, toYear]);

  // Update calendar view when year or month changes
  React.useEffect(() => {
    if (calendarDate) {
      const newDate = new Date(calendarDate);
      newDate.setFullYear(year);
      newDate.setMonth(month);
      setCalendarDate(newDate);
    }
  }, [year, month]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? (
            format(date, "d MMMM yyyy", { locale: id })
          ) : (
            <span>{placeholder}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3 border-b border-border flex justify-between items-center gap-2">
          {/* Month Dropdown */}
          <Select
            value={month.toString()}
            onValueChange={(value) => setMonth(parseInt(value))}
          >
            <SelectTrigger className="w-[130px] h-8 text-xs">
              <SelectValue placeholder="Pilih Bulan" />
            </SelectTrigger>
            <SelectContent>
              {[
                { value: "0", label: "Januari" },
                { value: "1", label: "Februari" },
                { value: "2", label: "Maret" },
                { value: "3", label: "April" },
                { value: "4", label: "Mei" },
                { value: "5", label: "Juni" },
                { value: "6", label: "Juli" },
                { value: "7", label: "Agustus" },
                { value: "8", label: "September" },
                { value: "9", label: "Oktober" },
                { value: "10", label: "November" },
                { value: "11", label: "Desember" },
              ].map((month) => (
                <SelectItem
                  key={month.value}
                  value={month.value}
                  className="text-xs"
                >
                  {month.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Year Dropdown */}
          <Select
            value={year.toString()}
            onValueChange={(value) => setYear(parseInt(value))}
          >
            <SelectTrigger className="w-[100px] h-8 text-xs">
              <SelectValue placeholder="Pilih Tahun" />
            </SelectTrigger>
            <SelectContent className="max-h-[200px]">
              {years.map((year) => (
                <SelectItem
                  key={year}
                  value={year.toString()}
                  className="text-xs"
                >
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          month={calendarDate}
          onMonthChange={setCalendarDate}
          initialFocus
          locale={id}
        />
      </PopoverContent>
    </Popover>
  );
}
