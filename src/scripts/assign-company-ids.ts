/**
 * This script assigns company IDs to all existing users who don't have one yet.
 * It should be run after the database migration that adds the companyId field.
 * 
 * To run this script:
 * 1. Make sure you have the latest database schema with the companyId field
 * 2. Run: bun src/scripts/assign-company-ids.ts
 */

import { db } from "@/lib/prisma";
import { assignCompanyIdsToExistingUsers } from "@/actions/users/company-id";

async function main() {
  console.log("Starting to assign company IDs to existing users...");
  
  try {
    const result = await assignCompanyIdsToExistingUsers();
    
    if (result.success) {
      console.log(`Successfully assigned company IDs to ${result.count} users.`);
    } else {
      console.error(`Failed to assign company IDs: ${result.error}`);
    }
  } catch (error) {
    console.error("Error running script:", error);
  } finally {
    await db.$disconnect();
  }
}

main()
  .then(() => {
    console.log("<PERSON><PERSON><PERSON> completed successfully.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("<PERSON><PERSON><PERSON> failed:", error);
    process.exit(1);
  });
