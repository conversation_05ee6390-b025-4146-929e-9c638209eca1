/**
 * This script creates a default warehouse for all existing users who don't have one yet.
 * It should be run after the warehouse management migration.
 * 
 * To run this script:
 * bun src/scripts/create-default-warehouse.ts
 */

import { db } from "@/lib/prisma";

async function createDefaultWarehouses() {
  console.log("🏭 Creating default warehouses for existing users...");
  
  try {
    // Get all users who don't have any warehouses yet
    const usersWithoutWarehouses = await db.user.findMany({
      where: {
        warehouses: {
          none: {}
        }
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true
      }
    });

    console.log(`Found ${usersWithoutWarehouses.length} users without warehouses.`);

    if (usersWithoutWarehouses.length === 0) {
      console.log("✅ All users already have warehouses. No action needed.");
      return;
    }

    let successCount = 0;
    let errorCount = 0;

    for (const user of usersWithoutWarehouses) {
      try {
        const identifier = user.email || user.username || user.name || user.id;
        
        await db.warehouse.create({
          data: {
            name: "<PERSON><PERSON><PERSON>",
            description: "Gudang default yang dibuat otomatis",
            isActive: true,
            isDefault: true,
            userId: user.id
          }
        });

        console.log(`✅ Created default warehouse for user: ${identifier}`);
        successCount++;
      } catch (error) {
        console.error(`❌ Failed to create warehouse for user ${user.id}:`, error);
        errorCount++;
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`  ✅ Successfully created: ${successCount} warehouses`);
    console.log(`  ❌ Failed: ${errorCount} warehouses`);
    
    if (successCount > 0) {
      console.log(`\n🎉 Default warehouses created successfully!`);
      console.log(`Users can now use the warehouse management features.`);
    }

  } catch (error) {
    console.error("❌ Error creating default warehouses:", error);
    throw error;
  }
}

async function migrateExistingStock() {
  console.log("\n📦 Migrating existing product stock to default warehouses...");
  
  try {
    // Get all products that have stock but no warehouse stock entries
    const productsWithStock = await db.product.findMany({
      where: {
        stock: {
          gt: 0
        },
        warehouseStocks: {
          none: {}
        }
      },
      include: {
        user: {
          include: {
            warehouses: {
              where: {
                isDefault: true
              }
            }
          }
        }
      }
    });

    console.log(`Found ${productsWithStock.length} products with stock to migrate.`);

    if (productsWithStock.length === 0) {
      console.log("✅ No stock migration needed.");
      return;
    }

    let migratedCount = 0;
    let skippedCount = 0;

    for (const product of productsWithStock) {
      try {
        const defaultWarehouse = product.user.warehouses[0];
        
        if (!defaultWarehouse) {
          console.log(`⚠️  Skipping product ${product.name} - user has no default warehouse`);
          skippedCount++;
          continue;
        }

        await db.warehouseStock.create({
          data: {
            productId: product.id,
            warehouseId: defaultWarehouse.id,
            quantity: product.stock,
            minLevel: 0
          }
        });

        // Create initial stock movement record
        await db.stockMovement.create({
          data: {
            type: "ADJUSTMENT",
            quantity: product.stock,
            previousStock: 0,
            newStock: product.stock,
            notes: "Initial stock migration from product table",
            productId: product.id,
            warehouseId: defaultWarehouse.id,
            userId: product.userId
          }
        });

        console.log(`✅ Migrated ${product.stock} units of "${product.name}" to ${defaultWarehouse.name}`);
        migratedCount++;
      } catch (error) {
        console.error(`❌ Failed to migrate stock for product ${product.name}:`, error);
        skippedCount++;
      }
    }

    console.log(`\n📊 Stock Migration Summary:`);
    console.log(`  ✅ Successfully migrated: ${migratedCount} products`);
    console.log(`  ⚠️  Skipped: ${skippedCount} products`);
    
    if (migratedCount > 0) {
      console.log(`\n🎉 Stock migration completed successfully!`);
      console.log(`Existing product stock is now tracked in warehouses.`);
    }

  } catch (error) {
    console.error("❌ Error migrating stock:", error);
    throw error;
  }
}

async function main() {
  console.log("🚀 Starting warehouse setup for existing users...\n");
  
  try {
    // Step 1: Create default warehouses
    await createDefaultWarehouses();
    
    // Step 2: Migrate existing stock
    await migrateExistingStock();
    
    console.log("\n✨ Warehouse setup completed successfully!");
    console.log("\nNext steps:");
    console.log("1. Start your application: bun dev");
    console.log("2. Navigate to Products > Gudang tab");
    console.log("3. Verify that warehouses are created and working");
    console.log("4. Users can now manage multiple warehouses and track stock per location");
    
  } catch (error) {
    console.error("💥 Script failed:", error);
    throw error;
  }
}

main()
  .then(() => {
    console.log("\n🎯 Script completed successfully.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n💥 Script failed:", error);
    process.exit(1);
  })
  .finally(async () => {
    await db.$disconnect();
  });
