// Excel styling constants and utilities for professional report formatting

export interface ExcelCellStyle {
  font?: {
    name?: string;
    size?: number;
    bold?: boolean;
    color?: { rgb: string };
  };
  fill?: {
    type?: string;
    fgColor?: { rgb: string };
    bgColor?: { rgb: string };
  };
  border?: {
    top?: { style: string; color: { rgb: string } };
    bottom?: { style: string; color: { rgb: string } };
    left?: { style: string; color: { rgb: string } };
    right?: { style: string; color: { rgb: string } };
  };
  alignment?: {
    horizontal?: string;
    vertical?: string;
    wrapText?: boolean;
  };
  numFmt?: string;
}

// Brand colors
export const BRAND_COLORS = {
  primary: "4F46E5", // Indigo-600
  secondary: "7C3AED", // Violet-600
  accent: "059669", // Emerald-600
  success: "10B981", // Emerald-500
  warning: "F59E0B", // Amber-500
  error: "EF4444", // Red-500
  // Sheet-specific colors
  sales: "059669", // Green for sales
  purchases: "DC2626", // Red for purchases
  products: "2563EB", // Blue for products
  customers: "7C3AED", // Purple for customers
  suppliers: "EA580C", // Orange for suppliers
  gray: {
    50: "F9FAFB",
    100: "F3F4F6",
    200: "E5E7EB",
    300: "D1D5DB",
    400: "9CA3AF",
    500: "6B7280",
    600: "4B5563",
    700: "374151",
    800: "1F2937",
    900: "111827",
  },
};

// Professional font settings
export const FONTS = {
  header: {
    name: "Segoe UI",
    size: 14,
    bold: true,
  },
  subheader: {
    name: "Segoe UI",
    size: 12,
    bold: true,
  },
  body: {
    name: "Segoe UI",
    size: 10,
    bold: false,
  },
  small: {
    name: "Segoe UI",
    size: 9,
    bold: false,
  },
};

// Predefined styles for different cell types
export const CELL_STYLES = {
  // Report title style
  reportTitle: {
    font: {
      name: FONTS.header.name,
      sz: 18,
      bold: true,
      color: { rgb: BRAND_COLORS.primary },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.gray[50] },
    },
    border: {
      bottom: { style: "thick", color: { rgb: BRAND_COLORS.primary } },
    },
  },

  // Section header style
  sectionHeader: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.primary },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
    },
  },

  // Table header style
  tableHeader: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.secondary },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
      wrapText: true,
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
    },
  },

  // Table data style (even rows)
  tableDataEven: {
    font: {
      name: FONTS.body.name,
      sz: FONTS.body.size,
      bold: false,
      color: { rgb: BRAND_COLORS.gray[800] },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: "FFFFFF" },
    },
    alignment: {
      horizontal: "left",
      vertical: "center",
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
    },
  },

  // Table data style (odd rows)
  tableDataOdd: {
    font: {
      name: FONTS.body.name,
      sz: FONTS.body.size,
      bold: false,
      color: { rgb: BRAND_COLORS.gray[800] },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.gray[50] },
    },
    alignment: {
      horizontal: "left",
      vertical: "center",
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[200] } },
    },
  },

  // Currency/number style
  currency: {
    font: {
      name: FONTS.body.name,
      sz: FONTS.body.size,
      bold: false,
      color: { rgb: BRAND_COLORS.gray[800] },
    },
    alignment: {
      horizontal: "right",
      vertical: "center",
    },
    numFmt: "#,##0",
  },

  // Date style
  date: {
    font: {
      name: FONTS.body.name,
      sz: FONTS.body.size,
      bold: false,
      color: { rgb: BRAND_COLORS.gray[800] },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
    numFmt: "dd/mm/yyyy",
  },

  // Summary/total style
  summary: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: BRAND_COLORS.primary },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.gray[100] },
    },
    alignment: {
      horizontal: "right",
      vertical: "center",
    },
    border: {
      top: { style: "thick", color: { rgb: BRAND_COLORS.primary } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[300] } },
    },
  },

  // Info/metadata style
  info: {
    font: {
      name: FONTS.small.name,
      sz: FONTS.small.size,
      bold: false,
      color: { rgb: BRAND_COLORS.gray[600] },
    },
    alignment: {
      horizontal: "left",
      vertical: "center",
    },
  },

  // Warning/highlight style
  highlight: {
    font: {
      name: FONTS.body.name,
      sz: FONTS.body.size,
      bold: true,
      color: { rgb: BRAND_COLORS.warning },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: "FEF3C7" }, // Yellow-100
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
  },
};

// Sheet-specific header styles
export const SHEET_HEADER_STYLES = {
  sales: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.sales },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
      wrapText: true,
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
    },
  },
  purchases: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.purchases },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
      wrapText: true,
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
    },
  },
  products: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.products },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
      wrapText: true,
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
    },
  },
  customers: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.customers },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
      wrapText: true,
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
    },
  },
  suppliers: {
    font: {
      name: FONTS.subheader.name,
      sz: FONTS.subheader.size,
      bold: true,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      patternType: "solid",
      fgColor: { rgb: BRAND_COLORS.suppliers },
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
      wrapText: true,
    },
    border: {
      top: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      bottom: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      left: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
      right: { style: "thin", color: { rgb: BRAND_COLORS.gray[400] } },
    },
  },
};

// Number format patterns
export const NUMBER_FORMATS = {
  currency: '"Rp "#,##0',
  currencyDecimal: '"Rp "#,##0.00',
  percentage: "0.00%",
  integer: "#,##0",
  decimal: "#,##0.00",
  date: "dd/mm/yyyy",
  datetime: "dd/mm/yyyy hh:mm",
};
