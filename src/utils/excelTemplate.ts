// Professional Excel template system for generating well-formatted reports

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
} from "./excelStyles";

// Note: Column configurations are now hardcoded directly in the export function
// to ensure all columns are always included in Excel exports

export interface ReportData {
  sales?: any[];
  purchases?: any[];
  products?: any[];
  customers?: any[];
  suppliers?: any[];
  summary?: {
    totalSales?: number;
    totalPurchases?: number;
    totalProducts?: number;
    totalCustomers?: number;
    totalSuppliers?: number;
    period?: string;
    generatedAt?: Date;
  };
  filters?: {
    dateRange?: string;
    startDate?: Date;
    endDate?: Date;
    category?: string;
    supplier?: string;
    customer?: string;
    status?: string;
  };
  reportType?: "harian" | "bulanan" | "tahunan";
}

export interface ExcelTemplateOptions {
  companyName?: string;
  reportTitle?: string;
  includeCharts?: boolean;
  includeSummary?: boolean;
  autoFitColumns?: boolean;
}

// Utility function to apply cell style
const applyCellStyle = (
  worksheet: XLSX.WorkSheet,
  cellRef: string,
  style: any
) => {
  if (!worksheet[cellRef]) {
    worksheet[cellRef] = { t: "s", v: "" };
  }
  worksheet[cellRef].s = style;
};

// Utility function to merge cells
const mergeCells = (worksheet: XLSX.WorkSheet, range: string) => {
  if (!worksheet["!merges"]) {
    worksheet["!merges"] = [];
  }
  worksheet["!merges"].push(XLSX.utils.decode_range(range));
};

// Utility function to set column widths
const setColumnWidths = (worksheet: XLSX.WorkSheet, widths: number[]) => {
  worksheet["!cols"] = widths.map((width) => ({ width }));
};

// Utility function to set row heights
const setRowHeights = (
  worksheet: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!worksheet["!rows"]) {
    worksheet["!rows"] = [];
  }
  Object.entries(heights).forEach(([row, height]) => {
    const rowIndex = parseInt(row) - 1;
    if (!worksheet["!rows"]![rowIndex]) {
      worksheet["!rows"]![rowIndex] = {};
    }
    worksheet["!rows"]![rowIndex].hpt = height;
  });
};

// Utility function to add auto filter
const addAutoFilter = (worksheet: XLSX.WorkSheet, range: string) => {
  worksheet["!autofilter"] = { ref: range };
};

// Utility function to freeze panes
const freezePanes = (worksheet: XLSX.WorkSheet, cell: string) => {
  worksheet["!freeze"] = XLSX.utils.decode_cell(cell);
};

// Utility function to apply enhanced borders to a range
const applyBordersToRange = (
  worksheet: XLSX.WorkSheet,
  startRow: number,
  endRow: number,
  startCol: number,
  endCol: number,
  borderStyle: any
) => {
  for (let row = startRow; row <= endRow; row++) {
    for (let col = startCol; col <= endCol; col++) {
      const cellRef = XLSX.utils.encode_cell({ r: row, c: col });
      if (!worksheet[cellRef]) {
        worksheet[cellRef] = { t: "s", v: "" };
      }
      if (!worksheet[cellRef].s) {
        worksheet[cellRef].s = {};
      }
      worksheet[cellRef].s.border = borderStyle;
    }
  }
};

// Format currency value
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

// Format date value
const formatDate = (date: string | Date): string => {
  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleDateString("id-ID");
  } catch (error) {
    return String(date);
  }
};

// Get sheet-specific header style
const getSheetHeaderStyle = (sheetName: string) => {
  const sheetType = sheetName.toLowerCase();
  if (sheetType.includes("penjualan") || sheetType.includes("sales")) {
    return SHEET_HEADER_STYLES.sales;
  } else if (
    sheetType.includes("pembelian") ||
    sheetType.includes("purchase")
  ) {
    return SHEET_HEADER_STYLES.purchases;
  } else if (sheetType.includes("produk") || sheetType.includes("product")) {
    return SHEET_HEADER_STYLES.products;
  } else if (
    sheetType.includes("pelanggan") ||
    sheetType.includes("customer")
  ) {
    return SHEET_HEADER_STYLES.customers;
  } else if (sheetType.includes("supplier")) {
    return SHEET_HEADER_STYLES.suppliers;
  }
  // Default to generic table header
  return CELL_STYLES.tableHeader;
};

// Note: Column configurations are now hardcoded directly in createProfessionalExcelReport
// to ensure all columns are always included in Excel exports

// Note: Column types are now hardcoded directly with each column definition

// Create professional summary sheet
const createSummarySheet = (
  data: ReportData,
  options: ExcelTemplateOptions
): XLSX.WorkSheet => {
  const companyName = options.companyName || "Kasir Online";
  const reportTitle = options.reportTitle || "Laporan Keuangan";
  const currentDate = new Date().toLocaleDateString("id-ID");

  // Create worksheet data with enhanced branding
  const wsData = [
    // Row 1-3: Enhanced company header
    [companyName],
    ["Sistem Manajemen Kasir Profesional"],
    [""],
    // Row 4: Report title
    [reportTitle],
    [""],
    // Row 5-6: Report info
    ["Informasi Laporan"],
    [""],
    // Row 7-12: Report details
    [
      "Jenis Laporan:",
      data.reportType
        ? data.reportType.charAt(0).toUpperCase() + data.reportType.slice(1)
        : "Kustom",
    ],
    ["Periode:", data.summary?.period || "Tidak ditentukan"],
    ["Tanggal Export:", currentDate],
    ["Dibuat oleh:", "Sistem Kasir Online"],
    [""],
    // Row 13-14: Filter info
    ["Filter yang Diterapkan"],
    [""],
    // Row 15-20: Filter details
    ["Rentang Tanggal:", data.filters?.dateRange || "Semua"],
    [
      "Tanggal Mulai:",
      data.filters?.startDate
        ? formatDate(data.filters.startDate)
        : "Tidak ditentukan",
    ],
    [
      "Tanggal Akhir:",
      data.filters?.endDate
        ? formatDate(data.filters.endDate)
        : "Tidak ditentukan",
    ],
    ["Kategori:", data.filters?.category || "Semua"],
    ["Supplier:", data.filters?.supplier || "Semua"],
    ["Pelanggan:", data.filters?.customer || "Semua"],
    ["Status:", data.filters?.status || "Semua"],
    [""],
    // Row 23-24: Summary stats
    ["Ringkasan Data"],
    [""],
    // Row 25-30: Summary details
    [
      "Total Penjualan:",
      data.summary?.totalSales
        ? formatCurrency(data.summary.totalSales)
        : "Rp 0",
    ],
    [
      "Total Pembelian:",
      data.summary?.totalPurchases
        ? formatCurrency(data.summary.totalPurchases)
        : "Rp 0",
    ],
    ["Total Produk:", data.summary?.totalProducts || 0],
    [""],
    [""],
    // Row 31: Footer
    ["Laporan ini dibuat secara otomatis oleh sistem Kasir Online"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(wsData);

  // Apply enhanced styles
  applyCellStyle(worksheet, "A1", CELL_STYLES.reportTitle);
  applyCellStyle(worksheet, "A2", CELL_STYLES.info);
  applyCellStyle(worksheet, "A4", CELL_STYLES.sectionHeader);
  applyCellStyle(worksheet, "A6", CELL_STYLES.sectionHeader);
  applyCellStyle(worksheet, "A14", CELL_STYLES.sectionHeader);
  applyCellStyle(worksheet, "A24", CELL_STYLES.sectionHeader);

  // Style data rows (adjusted for new row)
  for (let row = 8; row <= 13; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.info);
    applyCellStyle(worksheet, `B${row}`, CELL_STYLES.tableDataEven);
  }

  for (let row = 16; row <= 22; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.info);
    applyCellStyle(worksheet, `B${row}`, CELL_STYLES.tableDataEven);
  }

  for (let row = 26; row <= 28; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.info);
    applyCellStyle(worksheet, `B${row}`, CELL_STYLES.summary);
  }

  applyCellStyle(worksheet, "A32", CELL_STYLES.info);

  // Merge cells for headers (adjusted for new row)
  mergeCells(worksheet, "A1:B1");
  mergeCells(worksheet, "A2:B2");
  mergeCells(worksheet, "A4:B4");
  mergeCells(worksheet, "A6:B6");
  mergeCells(worksheet, "A14:B14");
  mergeCells(worksheet, "A24:B24");
  mergeCells(worksheet, "A32:B32");

  // Set column widths
  setColumnWidths(worksheet, [25, 30]);

  // Set row heights for headers (adjusted for new row)
  setRowHeights(worksheet, {
    1: 25,
    2: 16,
    4: 20,
    6: 18,
    14: 18,
    24: 18,
  });

  return worksheet;
};

// Create professional data sheet
const createDataSheet = (
  data: any[],
  sheetName: string,
  columns: {
    key: string;
    label: string;
    type?: "currency" | "date" | "number" | "text";
  }[]
): XLSX.WorkSheet => {
  // Use the provided columns (now always hardcoded in the main function)
  const finalColumns = columns;

  // Always create headers from ALL columns in configuration
  const headers = finalColumns.map((col) => col.label);

  // If no data, create empty rows but keep ALL column structure
  if (!data || data.length === 0) {
    // Create one empty row with ALL columns to maintain complete structure
    const emptyRow = finalColumns.map(() => "");
    const emptyData = [headers, emptyRow];
    const worksheet = XLSX.utils.aoa_to_sheet(emptyData);

    // Style headers with sheet-specific colors for ALL columns
    const headerStyle = getSheetHeaderStyle(sheetName);
    finalColumns.forEach((_, index) => {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
      applyCellStyle(worksheet, cellRef, headerStyle);
    });

    // Style empty row for ALL columns
    finalColumns.forEach((_, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: 1, c: colIndex });
      applyCellStyle(worksheet, cellRef, CELL_STYLES.tableDataEven);
    });

    // Auto-fit columns based on header length for ALL columns (wider headers)
    const columnWidths = finalColumns.map((col) =>
      Math.max(col.label.length + 8, 20)
    );
    setColumnWidths(worksheet, columnWidths);
    setRowHeights(worksheet, { 1: 20 }); // Header row height

    // Add auto filter even for empty sheet covering ALL columns
    const filterRange = `A1:${XLSX.utils.encode_col(finalColumns.length - 1)}2`;
    addAutoFilter(worksheet, filterRange);
    freezePanes(worksheet, "A2");

    return worksheet;
  }

  // Helper function to get nested property value
  const getNestedValue = (obj: any, path: string): any => {
    if (path.includes(".")) {
      const keys = path.split(".");
      let value = obj;
      for (const key of keys) {
        if (value === null || value === undefined) return null;
        value = value[key];
      }
      return value;
    }
    return obj[path];
  };

  // Create data rows ensuring ALL columns are included
  const rows = data.map((item) =>
    finalColumns.map((col) => {
      let value = getNestedValue(item, col.key);

      // Handle special cases
      if (col.key === "items.length" && Array.isArray(item.items)) {
        value = item.items.length;
      } else if (col.key === "customer.name") {
        value = item.customer?.name || "Umum";
      } else if (col.key === "supplier.name") {
        value = item.supplier?.name || "-";
      } else if (col.key === "category.name") {
        value = item.category?.name || "-";
      } else if (col.key === "tags" && Array.isArray(value)) {
        value = value.join(", ");
      } else if (col.key === "variants" && Array.isArray(item.variants)) {
        value = item.variants.map((v: any) => v.colorName).join(", ");
      }

      // Handle missing data gracefully - return empty string for missing fields
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        case "date":
          return formatDate(value);
        case "number":
          return typeof value === "number" ? value : 0;
        default:
          return String(value);
      }
    })
  );

  const wsData = [headers, ...rows];
  const worksheet = XLSX.utils.aoa_to_sheet(wsData);

  // Apply header styles with sheet-specific colors for ALL columns
  const headerStyle = getSheetHeaderStyle(sheetName);
  finalColumns.forEach((_, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    applyCellStyle(worksheet, cellRef, headerStyle);
  });

  // Apply data styles for ALL columns
  rows.forEach((_row, rowIndex) => {
    const actualRowIndex = rowIndex + 1; // +1 because of header
    const isEven = rowIndex % 2 === 0;

    finalColumns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({
        r: actualRowIndex,
        c: colIndex,
      });
      let style = isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd;

      // Apply specific formatting for different data types
      if (col.type === "currency") {
        style = {
          ...style,
          numFmt: NUMBER_FORMATS.currency,
          alignment: { horizontal: "right", vertical: "center" },
        } as any;
      } else if (col.type === "date") {
        style = {
          ...style,
          numFmt: NUMBER_FORMATS.date,
          alignment: { horizontal: "center", vertical: "center" },
        } as any;
      } else if (col.type === "number") {
        style = {
          ...style,
          numFmt: NUMBER_FORMATS.integer,
          alignment: { horizontal: "right", vertical: "center" },
        } as any;
      } else if (col.label === "Deskripsi" || col.key === "description") {
        // Special formatting for description column: enable text wrapping
        style = {
          ...style,
          alignment: {
            horizontal: "left",
            vertical: "top",
            wrapText: true,
          },
        } as any;
      }

      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // Auto-fit columns for ALL columns (wider headers to prevent cutoff)
  const columnWidths = finalColumns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength = Math.max(
      ...rows.map((row) => String(row[index] || "").length),
      0
    );

    // Special handling for description column to prevent it from being too wide
    if (col.label === "Deskripsi" || col.key === "description") {
      // Limit description column to a reasonable width (50 characters)
      // This allows for readable text without making the column excessively wide
      return Math.min(Math.max(headerLength + 8, 50), 50);
    }

    return Math.max(headerLength + 8, maxDataLength + 4, 20);
  });

  setColumnWidths(worksheet, columnWidths);

  // Set row heights - header row and data rows with description content
  const rowHeights: { [key: number]: number } = { 1: 20 }; // Header row height

  // Check if we have description column and set appropriate row heights for data rows
  const hasDescriptionColumn = finalColumns.some(
    (col) => col.label === "Deskripsi" || col.key === "description"
  );
  if (hasDescriptionColumn && data.length > 0) {
    // Set higher row height for data rows to accommodate wrapped text in description
    for (let i = 2; i <= data.length + 1; i++) {
      rowHeights[i] = 40; // Increased height for description content
    }
  }

  setRowHeights(worksheet, rowHeights);

  // Add interactive features if there's data
  if (data.length > 0) {
    // Add auto filter to header row covering ALL columns
    const filterRange = `A1:${XLSX.utils.encode_col(finalColumns.length - 1)}${data.length + 1}`;
    addAutoFilter(worksheet, filterRange);

    // Freeze header row
    freezePanes(worksheet, "A2");

    // Apply enhanced borders to the entire data range covering ALL columns
    const borderStyle = {
      top: { style: "thin", color: { rgb: "E5E7EB" } },
      bottom: { style: "thin", color: { rgb: "E5E7EB" } },
      left: { style: "thin", color: { rgb: "E5E7EB" } },
      right: { style: "thin", color: { rgb: "E5E7EB" } },
    };
    applyBordersToRange(
      worksheet,
      0,
      data.length,
      0,
      finalColumns.length - 1,
      borderStyle
    );
  }

  return worksheet;
};

// Create metadata sheet with technical information
const createMetadataSheet = (
  _data: ReportData,
  options: ExcelTemplateOptions
): XLSX.WorkSheet => {
  const companyName = options.companyName || "Kasir Online";
  const wsData = [
    [`Metadata Laporan - ${companyName}`],
    [""],
    ["Informasi Teknis"],
    [""],
    ["Sistem:", "Kasir Online Professional"],
    ["Versi:", "2.0.0"],
    ["Dibuat pada:", new Date().toISOString()],
    ["Format:", "Excel (.xlsx)"],
    ["Encoding:", "UTF-8"],
    ["Kompatibilitas:", "Excel 2016+"],
    [""],
    ["Statistik Data"],
    [""],
    ["Jumlah Sheet:", ""],
    ["Total Baris Data:", ""],
    ["Ukuran File:", "Estimasi: < 5MB"],
    [""],
    ["Fitur Excel"],
    [""],
    ["✓ Auto Filter pada header"],
    ["✓ Freeze Panes untuk navigasi"],
    ["✓ Styling profesional dengan warna tema"],
    ["✓ Format mata uang otomatis"],
    ["✓ Zebra striping untuk readability"],
    [""],
    ["Catatan Penting"],
    [""],
    ["• Data dalam laporan ini diambil dari database pada waktu export"],
    ["• Semua nilai mata uang dalam Rupiah (IDR)"],
    ["• Format tanggal: DD/MM/YYYY"],
    ["• Laporan ini dibuat secara otomatis"],
    ["• Untuk support, hubungi tim IT"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(wsData);

  // Apply enhanced styles
  applyCellStyle(worksheet, "A1", CELL_STYLES.reportTitle);
  applyCellStyle(worksheet, "A3", CELL_STYLES.sectionHeader);
  applyCellStyle(worksheet, "A12", CELL_STYLES.sectionHeader);
  applyCellStyle(worksheet, "A18", CELL_STYLES.sectionHeader);
  applyCellStyle(worksheet, "A26", CELL_STYLES.sectionHeader);

  // Style technical info rows
  for (let row = 5; row <= 10; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.info);
    applyCellStyle(worksheet, `B${row}`, CELL_STYLES.tableDataEven);
  }

  // Style statistics rows
  for (let row = 14; row <= 16; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.info);
    applyCellStyle(worksheet, `B${row}`, CELL_STYLES.tableDataEven);
  }

  // Style feature list with highlight
  for (let row = 20; row <= 24; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.highlight);
  }

  // Style notes
  for (let row = 28; row <= 32; row++) {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.info);
  }

  // Merge cells for headers
  mergeCells(worksheet, "A1:B1");
  mergeCells(worksheet, "A3:B3");
  mergeCells(worksheet, "A12:B12");
  mergeCells(worksheet, "A18:B18");
  mergeCells(worksheet, "A26:B26");

  setColumnWidths(worksheet, [35, 25]);
  setRowHeights(worksheet, { 1: 25, 3: 18, 12: 18, 18: 18, 26: 18 });

  return worksheet;
};

// Add summary statistics to data sheets
const addSummaryToDataSheet = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: { key: string; label: string; type?: string }[]
): void => {
  if (!data || data.length === 0) return;

  const lastRow = data.length + 2; // +1 for header, +1 for gap
  const summaryStartRow = lastRow + 1;

  // Add summary section
  const summaryData = [[""], ["RINGKASAN"], ["Total Baris:", data.length]];

  // Add currency totals if applicable
  const currencyColumns = columns.filter((col) => col.type === "currency");
  currencyColumns.forEach((col) => {
    const total = data.reduce((sum, item) => sum + (item[col.key] || 0), 0);
    summaryData.push([`Total ${col.label}:`, total]);
  });

  // Add number totals if applicable
  const numberColumns = columns.filter((col) => col.type === "number");
  numberColumns.forEach((col) => {
    const total = data.reduce((sum, item) => sum + (item[col.key] || 0), 0);
    summaryData.push([`Total ${col.label}:`, total]);
  });

  // Add summary data to worksheet
  summaryData.forEach((row, index) => {
    const rowIndex = summaryStartRow + index;
    row.forEach((cell, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
      worksheet[cellRef] = { t: typeof cell === "number" ? "n" : "s", v: cell };

      if (index === 1) {
        // Header row
        applyCellStyle(worksheet, cellRef, CELL_STYLES.sectionHeader);
      } else if (index > 1 && colIndex === 0) {
        // Label column
        applyCellStyle(worksheet, cellRef, CELL_STYLES.info);
      } else if (index > 1 && colIndex === 1) {
        // Value column
        const column = [...currencyColumns, ...numberColumns][index - 2];
        const style =
          column?.type === "currency"
            ? ({
                ...CELL_STYLES.summary,
                numFmt: NUMBER_FORMATS.currency,
              } as any)
            : CELL_STYLES.summary;
        applyCellStyle(worksheet, cellRef, style);
      }
    });
  });

  // Merge header cell
  if (summaryData.length > 1) {
    const headerRow = summaryStartRow + 1;
    mergeCells(
      worksheet,
      `A${headerRow}:${XLSX.utils.encode_col(columns.length - 1)}${headerRow}`
    );
  }
};

// Main function to create professional Excel report
export const createProfessionalExcelReport = (
  data: ReportData,
  options: ExcelTemplateOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  let sheetCount = 0;

  // Create summary sheet if enabled
  if (options.includeSummary !== false) {
    const summarySheet = createSummarySheet(data, options);
    XLSX.utils.book_append_sheet(workbook, summarySheet, "📊 Ringkasan");
    sheetCount++;
  }

  // Create sales sheet (always create, even if no data)
  // Use actual data if available, otherwise create empty sheet with ALL headers
  const salesData = data.sales && data.sales.length > 0 ? data.sales : [];

  // HARDCODED: All sales columns with correct field mappings
  const salesColumns = [
    { key: "id", label: "No. Transaksi", type: "text" as const },
    { key: "saleDate", label: "Tanggal", type: "date" as const },
    { key: "invoiceRef", label: "No. Faktur", type: "text" as const },
    { key: "paymentDueDate", label: "Tgl. Jatuh Tempo", type: "date" as const },
    { key: "customer.name", label: "Pelanggan", type: "text" as const },
    { key: "totalAmount", label: "Total", type: "currency" as const },
    { key: "items.length", label: "Jumlah Item", type: "number" as const },
    { key: "tags", label: "Tag", type: "text" as const },
  ];
  const salesSheet = createDataSheet(salesData, "Penjualan", salesColumns);

  // Only add summary if there's actual data
  if (salesData.length > 0) {
    addSummaryToDataSheet(salesSheet, salesData, salesColumns);
  }

  XLSX.utils.book_append_sheet(workbook, salesSheet, "💰 Penjualan");
  sheetCount++;

  // Create purchases sheet (always create, even if no data)
  // Use actual data if available, otherwise create empty sheet with ALL headers
  const purchasesData =
    data.purchases && data.purchases.length > 0 ? data.purchases : [];

  // HARDCODED: All purchases columns with correct field mappings
  const purchaseColumns = [
    { key: "id", label: "No. Transaksi", type: "text" as const },
    { key: "purchaseDate", label: "Tanggal", type: "date" as const },
    { key: "invoiceRef", label: "No. Faktur", type: "text" as const },
    { key: "paymentDueDate", label: "Tgl. Jatuh Tempo", type: "date" as const },
    { key: "supplier.name", label: "Supplier", type: "text" as const },
    { key: "totalAmount", label: "Total", type: "currency" as const },
    { key: "items.length", label: "Jumlah Item", type: "number" as const },
    { key: "tags", label: "Tag", type: "text" as const },
  ];
  const purchaseSheet = createDataSheet(
    purchasesData,
    "Pembelian",
    purchaseColumns
  );

  // Only add summary if there's actual data
  if (purchasesData.length > 0) {
    addSummaryToDataSheet(purchaseSheet, purchasesData, purchaseColumns);
  }

  XLSX.utils.book_append_sheet(workbook, purchaseSheet, "🛒 Pembelian");
  sheetCount++;

  // Create products sheet (always create, even if no data)
  // Use actual data if available, otherwise create empty sheet with ALL headers
  const productsData =
    data.products && data.products.length > 0 ? data.products : [];

  // HARDCODED: All products columns with correct field mappings
  const productColumns = [
    { key: "id", label: "ID Product", type: "text" as const },
    { key: "name", label: "Nama Produk", type: "text" as const },
    { key: "description", label: "Deskripsi", type: "text" as const },
    { key: "sku", label: "Kode Produk", type: "text" as const },
    { key: "barcode", label: "Barcode", type: "text" as const },
    { key: "unit", label: "Satuan", type: "text" as const },
    { key: "stock", label: "Total Stok", type: "number" as const },
    { key: "cost", label: "Harga Beli", type: "currency" as const },
    { key: "price", label: "Harga Jual", type: "currency" as const },
    { key: "wholesalePrice", label: "Harga Grosir", type: "currency" as const },
    { key: "discountPrice", label: "Harga Diskon", type: "currency" as const },
    { key: "category.name", label: "Kategori Produk", type: "text" as const },
    { key: "tags", label: "Tag Produk", type: "text" as const },
    { key: "variants", label: "Varian Warna", type: "text" as const },
  ];
  const productSheet = createDataSheet(productsData, "Produk", productColumns);

  // Only add summary if there's actual data
  if (productsData.length > 0) {
    addSummaryToDataSheet(productSheet, productsData, productColumns);
  }

  XLSX.utils.book_append_sheet(workbook, productSheet, "📦 Produk");
  sheetCount++;

  // Create customers sheet (always create, even if no data)
  // Use actual data if available, otherwise create empty sheet with ALL headers
  const customersData =
    data.customers && data.customers.length > 0 ? data.customers : [];

  // HARDCODED: All customers columns from customersColumnConfig
  const customerColumns = [
    { key: "id", label: "ID", type: "text" as const },
    { key: "name", label: "Nama Pelanggan", type: "text" as const },
    { key: "contactName", label: "Nama Kontak", type: "text" as const },
    { key: "email", label: "Email", type: "text" as const },
    { key: "phone", label: "Telepon", type: "text" as const },
    { key: "address", label: "Alamat", type: "text" as const },
    { key: "NIK", label: "NIK", type: "text" as const },
    { key: "NPWP", label: "NPWP", type: "text" as const },
    { key: "createdAt", label: "Tanggal Dibuat", type: "date" as const },
    { key: "updatedAt", label: "Tanggal Diperbarui", type: "date" as const },
    { key: "notes", label: "Catatan", type: "text" as const },
  ];
  const customerSheet = createDataSheet(
    customersData,
    "Pelanggan",
    customerColumns
  );

  // Only add summary if there's actual data
  if (customersData.length > 0) {
    addSummaryToDataSheet(customerSheet, customersData, customerColumns);
  }

  XLSX.utils.book_append_sheet(workbook, customerSheet, "👥 Pelanggan");
  sheetCount++;

  // Create suppliers sheet (always create, even if no data)
  // Use actual data if available, otherwise create empty sheet with ALL headers
  const suppliersData =
    data.suppliers && data.suppliers.length > 0 ? data.suppliers : [];

  // HARDCODED: All suppliers columns from suppliersColumnConfig
  const supplierColumns = [
    { key: "id", label: "ID", type: "text" as const },
    { key: "name", label: "Nama Supplier", type: "text" as const },
    { key: "contactName", label: "Nama Kontak", type: "text" as const },
    { key: "email", label: "Email", type: "text" as const },
    { key: "phone", label: "Telepon", type: "text" as const },
    { key: "address", label: "Alamat", type: "text" as const },
    { key: "createdAt", label: "Tanggal Dibuat", type: "date" as const },
    { key: "updatedAt", label: "Tanggal Diperbarui", type: "date" as const },
    { key: "notes", label: "Catatan", type: "text" as const },
  ];
  const supplierSheet = createDataSheet(
    suppliersData,
    "Supplier",
    supplierColumns
  );

  // Only add summary if there's actual data
  if (suppliersData.length > 0) {
    addSummaryToDataSheet(supplierSheet, suppliersData, supplierColumns);
  }

  XLSX.utils.book_append_sheet(workbook, supplierSheet, "🏢 Supplier");
  sheetCount++;

  // Create metadata sheet
  const metadataSheet = createMetadataSheet(data, options);
  // Update sheet count in metadata
  if (metadataSheet["B13"]) {
    metadataSheet["B13"].v = sheetCount + 1; // +1 for metadata sheet itself
  }
  // Update total rows count
  const totalRows =
    (data.sales?.length || 0) +
    (data.purchases?.length || 0) +
    (data.products?.length || 0) +
    (data.customers?.length || 0) +
    (data.suppliers?.length || 0);
  if (metadataSheet["B14"]) {
    metadataSheet["B14"].v = totalRows;
  }

  XLSX.utils.book_append_sheet(workbook, metadataSheet, "ℹ️ Metadata");

  return workbook;
};
