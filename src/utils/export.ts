import { Customer } from "@/components/pages/dashboard/customers/types";
import { Supplier } from "@/components/pages/dashboard/suppliers/types";
import * as XLSX from "xlsx-js-style";
import { createProfessionalExcelReport } from "./excelTemplate";

// Convert data to CSV format
export const convertToCSV = (data: any[], headers: string[]): string => {
  const csvHeaders = headers.join(",");
  const csvRows = data.map((row) =>
    headers
      .map((header) => {
        const value = row[header];
        // Handle null/undefined values
        if (value === null || value === undefined) return "";
        // Handle dates
        if (value instanceof Date) return value.toISOString().split("T")[0];
        // Escape commas and quotes in strings
        if (typeof value === "string") {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value.toString();
      })
      .join(",")
  );

  return [csvHeaders, ...csvRows].join("\n");
};

// Download CSV file
export const downloadCSV = (csvContent: string, filename: string): void => {
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Export customers to CSV
export const exportCustomersToCSV = (customers: Customer[]): void => {
  const headers = [
    "name",
    "contactName",
    "email",
    "phone",
    "address",
    "NIK",
    "NPWP",
    "notes",
    "createdAt",
    "updatedAt",
  ];

  const csvContent = convertToCSV(customers, headers);
  const filename = `customers_${new Date().toISOString().split("T")[0]}.csv`;
  downloadCSV(csvContent, filename);
};

// Export suppliers to CSV
export const exportSuppliersToCSV = (suppliers: Supplier[]): void => {
  const headers = [
    "name",
    "contactName",
    "email",
    "phone",
    "address",
    "notes",
    "createdAt",
    "updatedAt",
  ];

  const csvContent = convertToCSV(suppliers, headers);
  const filename = `suppliers_${new Date().toISOString().split("T")[0]}.csv`;
  downloadCSV(csvContent, filename);
};

// Export customers to professional Excel format
export const exportCustomersToExcel = (customers: Customer[]): void => {
  const reportData = {
    customers: customers.map((customer) => ({
      id: customer.id,
      name: customer.name,
      contactName: customer.contactName || "-",
      email: customer.email || "-",
      phone: customer.phone || "-",
      address: customer.address || "-",
      nik: customer.NIK || "-",
      npwp: customer.NPWP || "-",
      notes: customer.notes || "-",
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    })),
    summary: {
      totalCustomers: customers.length,
      period: "Semua Data",
      generatedAt: new Date(),
    },
  };

  // Create a custom version for customers
  const workbook = XLSX.utils.book_new();

  // Summary sheet
  const summaryData = [
    ["Data Pelanggan - Kasir Online"],
    [""],
    ["Informasi Export"],
    [""],
    ["Total Pelanggan:", customers.length],
    ["Tanggal Export:", new Date().toLocaleDateString("id-ID")],
    ["Format:", "Excel (.xlsx)"],
    [""],
    ["Catatan"],
    [""],
    ["• Data pelanggan yang terdaftar dalam sistem"],
    ["• Informasi kontak dan identitas pelanggan"],
    ["• Data diambil pada waktu export"],
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, "📊 Ringkasan");

  // Customers data sheet
  if (customers.length > 0) {
    const customerColumns = [
      { key: "id", label: "ID" },
      { key: "name", label: "Nama Pelanggan" },
      { key: "contactName", label: "Nama Kontak" },
      { key: "email", label: "Email" },
      { key: "phone", label: "Telepon" },
      { key: "address", label: "Alamat" },
      { key: "nik", label: "NIK" },
      { key: "npwp", label: "NPWP" },
      { key: "notes", label: "Catatan" },
      { key: "createdAt", label: "Dibuat" },
      { key: "updatedAt", label: "Diperbarui" },
    ];

    const headers = customerColumns.map((col) => col.label);
    const rows = reportData.customers.map((customer) =>
      customerColumns.map(
        (col) => customer[col.key as keyof typeof customer] || ""
      )
    );

    const wsData = [headers, ...rows];
    const customerSheet = XLSX.utils.aoa_to_sheet(wsData);
    XLSX.utils.book_append_sheet(workbook, customerSheet, "👥 Data Pelanggan");
  }

  const filename = `data-pelanggan-${new Date().toISOString().split("T")[0]}.xlsx`;
  XLSX.writeFile(workbook, filename);
};

// Export suppliers to professional Excel format
export const exportSuppliersToExcel = (suppliers: Supplier[]): void => {
  const reportData = {
    suppliers: suppliers.map((supplier) => ({
      id: supplier.id,
      name: supplier.name,
      contactName: supplier.contactName || "-",
      email: supplier.email || "-",
      phone: supplier.phone || "-",
      address: supplier.address || "-",
      notes: supplier.notes || "-",
      createdAt: supplier.createdAt,
      updatedAt: supplier.updatedAt,
    })),
    summary: {
      totalSuppliers: suppliers.length,
      period: "Semua Data",
      generatedAt: new Date(),
    },
  };

  // Create a custom version for suppliers
  const workbook = XLSX.utils.book_new();

  // Summary sheet
  const summaryData = [
    ["Data Supplier - Kasir Online"],
    [""],
    ["Informasi Export"],
    [""],
    ["Total Supplier:", suppliers.length],
    ["Tanggal Export:", new Date().toLocaleDateString("id-ID")],
    ["Format:", "Excel (.xlsx)"],
    [""],
    ["Catatan"],
    [""],
    ["• Data supplier yang terdaftar dalam sistem"],
    ["• Informasi kontak dan detail supplier"],
    ["• Data diambil pada waktu export"],
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, "📊 Ringkasan");

  // Suppliers data sheet
  if (suppliers.length > 0) {
    const supplierColumns = [
      { key: "id", label: "ID" },
      { key: "name", label: "Nama Supplier" },
      { key: "contactName", label: "Nama Kontak" },
      { key: "email", label: "Email" },
      { key: "phone", label: "Telepon" },
      { key: "address", label: "Alamat" },
      { key: "notes", label: "Catatan" },
      { key: "createdAt", label: "Dibuat" },
      { key: "updatedAt", label: "Diperbarui" },
    ];

    const headers = supplierColumns.map((col) => col.label);
    const rows = reportData.suppliers.map((supplier) =>
      supplierColumns.map(
        (col) => supplier[col.key as keyof typeof supplier] || ""
      )
    );

    const wsData = [headers, ...rows];
    const supplierSheet = XLSX.utils.aoa_to_sheet(wsData);
    XLSX.utils.book_append_sheet(workbook, supplierSheet, "🏢 Data Supplier");
  }

  const filename = `data-supplier-${new Date().toISOString().split("T")[0]}.xlsx`;
  XLSX.writeFile(workbook, filename);
};

// Parse CSV content
export const parseCSV = (csvContent: string): any[] => {
  const lines = csvContent.split("\n").filter((line) => line.trim());
  if (lines.length === 0) return [];

  const headers = lines[0]
    .split(",")
    .map((header) => header.trim().replace(/"/g, ""));
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i]
      .split(",")
      .map((value) => value.trim().replace(/"/g, ""));
    const row: any = {};

    headers.forEach((header, index) => {
      const value = values[index] || "";
      row[header] = value === "" ? null : value;
    });

    data.push(row);
  }

  return data;
};

// Handle file upload
export const handleFileUpload = (
  file: File,
  onSuccess: (data: any[]) => void,
  onError: (error: string) => void
): void => {
  if (!file) {
    onError("No file selected");
    return;
  }

  if (!file.name.endsWith(".csv")) {
    onError("Please select a CSV file");
    return;
  }

  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const csvContent = e.target?.result as string;
      const data = parseCSV(csvContent);
      onSuccess(data);
    } catch (error) {
      onError("Error parsing CSV file");
    }
  };

  reader.onerror = () => {
    onError("Error reading file");
  };

  reader.readAsText(file);
};
