import * as XLSX from "xlsx-js-style";
import { CELL_STYLES, NUMBER_FORMATS } from "./excelStyles";

// Create Excel import template for products
export const createProductImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns (excluding ID Product and Total Stok as per requirements)
  const columns = [
    {
      key: "Nama Produk",
      label: "Nama Produk",
      required: true,
      type: "text",
      example: "Kopi Arabica Premium",
    },
    {
      key: "Deskripsi",
      label: "Deskripsi",
      required: false,
      type: "text",
      example:
        "Kopi arabica premium berkualitas tinggi dengan cita rasa yang khas",
    },
    {
      key: "Kode Produk",
      label: "Kode Produk (SKU)",
      required: false,
      type: "text",
      example: "KAP-001",
    },
    {
      key: "Barcode",
      label: "Barcode",
      required: false,
      type: "text",
      example: "1234567890123",
    },
    {
      key: "Kategori",
      label: "Kategori",
      required: false,
      type: "text",
      example: "Minuman",
    },
    {
      key: "Satuan",
      label: "Satuan",
      required: false,
      type: "text",
      example: "Kg",
    },
    {
      key: "Harga Beli",
      label: "Harga Beli",
      required: false,
      type: "currency",
      example: 50000,
    },
    {
      key: "Harga Jual",
      label: "Harga Jual",
      required: true,
      type: "currency",
      example: 75000,
    },
    {
      key: "Harga Grosir",
      label: "Harga Grosir",
      required: false,
      type: "currency",
      example: 65000,
    },
    {
      key: "Harga Diskon",
      label: "Harga Diskon",
      required: false,
      type: "currency",
      example: 60000,
    },
    {
      key: "Tag Produk",
      label: "Tag Produk",
      required: false,
      type: "text",
      example: "premium,organik,bestseller",
    },
    {
      key: "Varian Warna",
      label: "Varian Warna",
      required: false,
      type: "text",
      example: "Merah,Biru,Hijau",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "Teh Hijau Organik",
      "Teh hijau organik pilihan dengan antioksidan tinggi untuk kesehatan",
      "THO-002",
      "2345678901234",
      "Minuman",
      "Box",
      30000,
      45000,
      40000,
      35000,
      "organik,sehat",
      "Original,Mint",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    const headerStyle = {
      ...CELL_STYLES.tableHeader,
      fill: {
        fgColor: { rgb: col.required ? "FFE6E6" : "E6F3FF" }, // Red for required, blue for optional
      },
    };
    applyCellStyle(worksheet, cellRef, headerStyle);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    for (let row = 1; row <= 2; row++) {
      const cellRef = XLSX.utils.encode_cell({ r: row, c: index });
      const exampleStyle = {
        ...CELL_STYLES.tableDataEven,
        fill: { fgColor: { rgb: "F0F8FF" } }, // Light blue for examples
        font: { ...CELL_STYLES.tableDataEven.font, italic: true },
      };
      applyCellStyle(worksheet, cellRef, exampleStyle);

      // Apply currency format for currency columns
      if (col.type === "currency" && worksheet[cellRef]) {
        worksheet[cellRef].z = NUMBER_FORMATS.currency;
      }
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Produk");

  // Create instructions sheet
  const instructionsSheet = createInstructionsSheet(columns);
  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  return workbook;
};

// Create instructions sheet
const createInstructionsSheet = (columns: any[]): XLSX.WorkSheet => {
  const instructions = [
    ["PETUNJUK PENGGUNAAN TEMPLATE IMPORT PRODUK"],
    [""],
    ["INFORMASI UMUM:"],
    ["• Template ini digunakan untuk mengimpor data produk ke sistem"],
    ["• Pastikan format data sesuai dengan contoh yang diberikan"],
    ["• Maksimal 1000 produk dapat diimpor dalam satu kali proses"],
    ["• Data akan ditambahkan ke akun Anda saat ini"],
    [""],
    ["KOLOM WAJIB (Harus diisi):"],
    ...columns
      .filter((col) => col.required)
      .map((col) => [`• ${col.label}: ${getColumnDescription(col)}`]),
    [""],
    ["KOLOM OPSIONAL (Boleh kosong):"],
    ...columns
      .filter((col) => !col.required)
      .map((col) => [`• ${col.label}: ${getColumnDescription(col)}`]),
    [""],
    ["FORMAT DATA:"],
    ["• Nama Produk: Maksimal 255 karakter"],
    ["• Deskripsi: Maksimal 1000 karakter, boleh kosong"],
    ["• Kode Produk (SKU): Unik per akun, akan dibuat otomatis jika duplikat"],
    ["• Barcode: Unik per akun, akan dibuat otomatis jika duplikat"],
    ["• Kategori: Akan dibuat otomatis jika belum ada"],
    ["• Satuan: Akan dibuat otomatis jika belum ada (default: Pcs)"],
    ["• Harga: Gunakan angka tanpa titik/koma (contoh: 50000)"],
    ["• Tag Produk: Pisahkan dengan koma (contoh: premium,organik,bestseller)"],
    ["• Varian Warna: Pisahkan dengan koma (contoh: Merah,Biru,Hijau)"],
    [""],
    ["CATATAN PENTING:"],
    ["• ID Produk akan dibuat otomatis oleh sistem"],
    ["• Stok awal akan diset ke 0 (kelola melalui pembelian)"],
    ["• Jika kategori/satuan belum ada, akan dibuat otomatis"],
    ["• Data yang duplikat akan dibuat unik secara otomatis"],
    ["• Proses import menggunakan transaksi database untuk keamanan"],
    [""],
    ["LANGKAH IMPORT:"],
    ["1. Isi data produk pada sheet 'Template Produk'"],
    ["2. Hapus baris contoh (baris 2-3) sebelum import"],
    ["3. Simpan file dalam format Excel (.xlsx)"],
    ["4. Upload file melalui tombol Import di sistem"],
    ["5. Tunggu proses selesai dan periksa hasilnya"],
    [""],
    ["TROUBLESHOOTING:"],
    ["• Jika import gagal, periksa format data sesuai petunjuk"],
    ["• Pastikan kolom wajib terisi semua"],
    ["• Periksa format angka untuk kolom harga"],
    ["• Maksimal file size: 10MB"],
    ["• Jika masih bermasalah, hubungi support"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(instructions);

  // Apply styles
  applyCellStyle(worksheet, "A1", {
    ...CELL_STYLES.reportTitle,
    font: { ...CELL_STYLES.reportTitle.font, size: 16 },
  });

  // Style section headers
  const sectionHeaders = [3, 9, 12, 16, 25, 31, 37];
  sectionHeaders.forEach((row) => {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.sectionHeader);
  });

  // Set column width
  setColumnWidths(worksheet, [80]);

  return worksheet;
};

// Get column description for instructions
const getColumnDescription = (col: any): string => {
  switch (col.key) {
    case "Nama Produk":
      return "Nama produk yang akan ditampilkan";
    case "Deskripsi":
      return "Deskripsi detail produk (maksimal 1000 karakter)";
    case "Kode Produk":
      return "Kode unik produk (SKU)";
    case "Barcode":
      return "Kode barcode produk";
    case "Kategori":
      return "Kategori produk (akan dibuat jika belum ada)";
    case "Satuan":
      return "Unit satuan produk (Pcs, Kg, Box, dll)";
    case "Harga Beli":
      return "Harga pembelian/modal produk";
    case "Harga Jual":
      return "Harga jual utama produk";
    case "Harga Grosir":
      return "Harga untuk penjualan grosir";
    case "Harga Diskon":
      return "Harga setelah diskon";
    case "Tag Produk":
      return "Label/tag produk, pisahkan dengan koma";
    case "Varian Warna":
      return "Varian warna produk, pisahkan dengan koma";
    default:
      return "Deskripsi tidak tersedia";
  }
};

// Utility functions (same as in excelTemplate.ts)
const applyCellStyle = (
  worksheet: XLSX.WorkSheet,
  cellRef: string,
  style: any
) => {
  if (!worksheet[cellRef]) {
    worksheet[cellRef] = { t: "s", v: "" };
  }
  worksheet[cellRef].s = style;
};

const setColumnWidths = (worksheet: XLSX.WorkSheet, widths: number[]) => {
  worksheet["!cols"] = widths.map((width) => ({ width }));
};

const addAutoFilter = (worksheet: XLSX.WorkSheet, range: string) => {
  worksheet["!autofilter"] = { ref: range };
};

const freezePanes = (worksheet: XLSX.WorkSheet, cell: string) => {
  worksheet["!freeze"] = XLSX.utils.decode_cell(cell);
};

// Create Excel import template for purchases
export const createPurchaseImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns for purchases
  const columns = [
    {
      key: "Tanggal Pembelian",
      label: "Tanggal Pembelian",
      required: true,
      type: "date",
      example: "2024-01-15",
    },
    {
      key: "Supplier",
      label: "Supplier",
      required: false,
      type: "text",
      example: "PT Supplier Utama",
    },
    {
      key: "No. Invoice",
      label: "No. Invoice",
      required: false,
      type: "text",
      example: "INV-2024-001",
    },
    {
      key: "Nama Produk",
      label: "Nama Produk",
      required: true,
      type: "text",
      example: "Kopi Arabica Premium",
    },
    {
      key: "Quantity",
      label: "Quantity",
      required: true,
      type: "number",
      example: 10,
    },
    {
      key: "Satuan",
      label: "Satuan",
      required: false,
      type: "text",
      example: "Kg",
    },
    {
      key: "Harga Beli",
      label: "Harga Beli",
      required: true,
      type: "currency",
      example: 50000,
    },
    {
      key: "Diskon (%)",
      label: "Diskon (%)",
      required: false,
      type: "number",
      example: 5,
    },
    {
      key: "Diskon (Rp)",
      label: "Diskon (Rp)",
      required: false,
      type: "currency",
      example: 2500,
    },
    {
      key: "PPN (%)",
      label: "PPN (%)",
      required: false,
      type: "number",
      example: 11,
    },
    {
      key: "Gudang",
      label: "Gudang",
      required: false,
      type: "text",
      example: "Gudang Utama",
    },
    {
      key: "Memo",
      label: "Memo",
      required: false,
      type: "text",
      example: "Pembelian rutin bulanan",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "2024-01-16",
      "CV Distributor Jaya",
      "INV-2024-002",
      "Teh Hijau Organik",
      25,
      "Box",
      30000,
      10,
      3000,
      11,
      "Gudang Cabang",
      "Stok untuk promosi",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((_, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    applyCellStyle(worksheet, cellRef, CELL_STYLES.tableHeader);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 1, c: index });
    if (col.type === "currency" || col.type === "number") {
      applyCellStyle(worksheet, cellRef, {
        ...CELL_STYLES.tableDataEven,
        numFmt:
          col.type === "currency"
            ? NUMBER_FORMATS.currency
            : NUMBER_FORMATS.integer,
      });
    } else {
      applyCellStyle(worksheet, cellRef, CELL_STYLES.tableDataEven);
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Pembelian");

  // Create instructions sheet
  const instructionsSheet = createPurchaseInstructionsSheet(columns);
  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  return workbook;
};

// Create instructions sheet for purchases
const createPurchaseInstructionsSheet = (columns: any[]): XLSX.WorkSheet => {
  const instructions = [
    ["PETUNJUK PENGGUNAAN TEMPLATE IMPORT PEMBELIAN"],
    [""],
    ["INFORMASI UMUM:"],
    ["• Template ini digunakan untuk mengimpor data pembelian ke sistem"],
    ["• Pastikan format data sesuai dengan contoh yang diberikan"],
    ["• Maksimal 500 transaksi pembelian dapat diimpor dalam satu kali proses"],
    ["• Data akan ditambahkan ke akun Anda saat ini"],
    [""],
    ["KOLOM WAJIB (Harus diisi):"],
    ...columns
      .filter((col) => col.required)
      .map((col) => [`• ${col.label}: ${getPurchaseColumnDescription(col)}`]),
    [""],
    ["KOLOM OPSIONAL (Boleh kosong):"],
    ...columns
      .filter((col) => !col.required)
      .map((col) => [`• ${col.label}: ${getPurchaseColumnDescription(col)}`]),
    [""],
    ["FORMAT DATA:"],
    ["• Tanggal Pembelian: Format YYYY-MM-DD (contoh: 2024-01-15)"],
    ["• Supplier: Nama supplier, akan dibuat otomatis jika belum ada"],
    ["• No. Invoice: Nomor invoice dari supplier"],
    ["• Nama Produk: Harus sesuai dengan produk yang sudah ada di sistem"],
    ["• Quantity: Jumlah barang yang dibeli (angka positif)"],
    ["• Satuan: Unit barang (Kg, Box, Pcs, dll)"],
    ["• Harga Beli: Harga per unit tanpa titik/koma (contoh: 50000)"],
    ["• Diskon: Bisa dalam persen (5) atau rupiah (2500)"],
    ["• PPN: Persentase pajak (contoh: 11 untuk 11%)"],
    ["• Gudang: Nama gudang tujuan"],
    ["• Memo: Catatan tambahan untuk transaksi"],
    [""],
    ["CATATAN PENTING:"],
    ["• ID Transaksi akan dibuat otomatis oleh sistem"],
    ["• Produk harus sudah ada di sistem sebelum import"],
    ["• Stok akan otomatis bertambah sesuai quantity pembelian"],
    ["• Supplier baru akan dibuat otomatis jika belum ada"],
    ["• Gudang baru akan dibuat otomatis jika belum ada"],
    ["• Total akan dihitung otomatis: (Quantity × Harga) - Diskon + PPN"],
    [""],
    ["TIPS IMPORT:"],
    ["• Periksa data sebelum import untuk menghindari kesalahan"],
    ["• Gunakan format tanggal yang konsisten"],
    ["• Pastikan nama produk sesuai dengan yang ada di sistem"],
    ["• Backup data sebelum melakukan import besar"],
    ["• Import dalam batch kecil untuk performa optimal"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(instructions);

  // Apply styles
  applyCellStyle(worksheet, "A1", {
    ...CELL_STYLES.reportTitle,
    font: { ...CELL_STYLES.reportTitle.font, size: 16 },
  });

  // Style section headers
  const sectionHeaders = [3, 9, 12, 16, 25, 31, 37];
  sectionHeaders.forEach((row) => {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.sectionHeader);
  });

  // Set column width
  setColumnWidths(worksheet, [80]);

  return worksheet;
};

// Get column description for purchases
const getPurchaseColumnDescription = (column: any): string => {
  switch (column.key) {
    case "Tanggal Pembelian":
      return "Tanggal transaksi pembelian (format: YYYY-MM-DD)";
    case "Supplier":
      return "Nama supplier/pemasok barang";
    case "No. Invoice":
      return "Nomor invoice dari supplier";
    case "Nama Produk":
      return "Nama produk yang dibeli (harus sudah ada di sistem)";
    case "Quantity":
      return "Jumlah barang yang dibeli";
    case "Satuan":
      return "Unit satuan barang (Kg, Box, Pcs, dll)";
    case "Harga Beli":
      return "Harga beli per unit dalam rupiah";
    case "Diskon (%)":
      return "Diskon dalam persentase (0-100)";
    case "Diskon (Rp)":
      return "Diskon dalam rupiah";
    case "PPN (%)":
      return "Pajak PPN dalam persentase";
    case "Gudang":
      return "Nama gudang tujuan penyimpanan";
    case "Memo":
      return "Catatan tambahan untuk transaksi";
    default:
      return "Deskripsi tidak tersedia";
  }
};

// Create Excel import template for sales
export const createSalesImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Define template columns for sales
  const columns = [
    {
      key: "Tanggal Penjualan",
      label: "Tanggal Penjualan",
      required: true,
      type: "date",
      example: "2024-01-15",
    },
    {
      key: "Pelanggan",
      label: "Pelanggan",
      required: false,
      type: "text",
      example: "John Doe",
    },
    {
      key: "No. Faktur",
      label: "No. Faktur",
      required: false,
      type: "text",
      example: "FAK-2024-001",
    },
    {
      key: "Nama Produk",
      label: "Nama Produk",
      required: true,
      type: "text",
      example: "Kopi Arabica Premium",
    },
    {
      key: "Quantity",
      label: "Quantity",
      required: true,
      type: "number",
      example: 5,
    },
    {
      key: "Satuan",
      label: "Satuan",
      required: false,
      type: "text",
      example: "Kg",
    },
    {
      key: "Harga Jual",
      label: "Harga Jual",
      required: true,
      type: "currency",
      example: 75000,
    },
    {
      key: "Diskon (%)",
      label: "Diskon (%)",
      required: false,
      type: "number",
      example: 10,
    },
    {
      key: "Diskon (Rp)",
      label: "Diskon (Rp)",
      required: false,
      type: "currency",
      example: 7500,
    },
    {
      key: "PPN (%)",
      label: "PPN (%)",
      required: false,
      type: "number",
      example: 11,
    },
    {
      key: "Harga Grosir",
      label: "Harga Grosir",
      required: false,
      type: "boolean",
      example: "Ya",
    },
    {
      key: "Gudang",
      label: "Gudang",
      required: false,
      type: "text",
      example: "Gudang Utama",
    },
    {
      key: "Memo",
      label: "Memo",
      required: false,
      type: "text",
      example: "Penjualan reguler",
    },
  ];

  // Create main template sheet
  const templateData = [
    // Header row
    columns.map((col) => col.label),
    // Example row 1
    columns.map((col) => col.example),
    // Example row 2
    [
      "2024-01-16",
      "Jane Smith",
      "FAK-2024-002",
      "Teh Hijau Organik",
      10,
      "Box",
      45000,
      5,
      2250,
      11,
      "Tidak",
      "Gudang Cabang",
      "Penjualan promosi",
    ],
    // Empty rows for user input
    ...Array(10).fill(columns.map(() => "")),
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styles
  columns.forEach((_, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    applyCellStyle(worksheet, cellRef, CELL_STYLES.tableHeader);
  });

  // Apply example row styles
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 1, c: index });
    if (col.type === "currency" || col.type === "number") {
      applyCellStyle(worksheet, cellRef, {
        ...CELL_STYLES.tableDataEven,
        numFmt:
          col.type === "currency"
            ? NUMBER_FORMATS.currency
            : NUMBER_FORMATS.integer,
      });
    } else {
      applyCellStyle(worksheet, cellRef, CELL_STYLES.tableDataEven);
    }
  });

  // Set column widths
  const columnWidths = columns.map((col) => Math.max(col.label.length + 5, 15));
  setColumnWidths(worksheet, columnWidths);

  // Add auto filter
  const filterRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}${templateData.length}`;
  addAutoFilter(worksheet, filterRange);

  // Freeze header row
  freezePanes(worksheet, "A2");

  XLSX.utils.book_append_sheet(workbook, worksheet, "Template Penjualan");

  // Create instructions sheet
  const instructionsSheet = createSalesInstructionsSheet(columns);
  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  return workbook;
};

// Create instructions sheet for sales
const createSalesInstructionsSheet = (columns: any[]): XLSX.WorkSheet => {
  const instructions = [
    ["PETUNJUK PENGGUNAAN TEMPLATE IMPORT PENJUALAN"],
    [""],
    ["INFORMASI UMUM:"],
    ["• Template ini digunakan untuk mengimpor data penjualan ke sistem"],
    ["• Pastikan format data sesuai dengan contoh yang diberikan"],
    ["• Maksimal 500 transaksi penjualan dapat diimpor dalam satu kali proses"],
    ["• Data akan ditambahkan ke akun Anda saat ini"],
    [""],
    ["KOLOM WAJIB (Harus diisi):"],
    ...columns
      .filter((col) => col.required)
      .map((col) => [`• ${col.label}: ${getSalesColumnDescription(col)}`]),
    [""],
    ["KOLOM OPSIONAL (Boleh kosong):"],
    ...columns
      .filter((col) => !col.required)
      .map((col) => [`• ${col.label}: ${getSalesColumnDescription(col)}`]),
    [""],
    ["FORMAT DATA:"],
    ["• Tanggal Penjualan: Format YYYY-MM-DD (contoh: 2024-01-15)"],
    ["• Pelanggan: Nama pelanggan, akan dibuat otomatis jika belum ada"],
    ["• No. Faktur: Nomor faktur penjualan"],
    ["• Nama Produk: Harus sesuai dengan produk yang sudah ada di sistem"],
    ["• Quantity: Jumlah barang yang dijual (angka positif)"],
    ["• Satuan: Unit barang (Kg, Box, Pcs, dll)"],
    ["• Harga Jual: Harga per unit tanpa titik/koma (contoh: 75000)"],
    ["• Diskon: Bisa dalam persen (10) atau rupiah (7500)"],
    ["• PPN: Persentase pajak (contoh: 11 untuk 11%)"],
    ["• Harga Grosir: Ya/Tidak untuk menggunakan harga grosir"],
    ["• Gudang: Nama gudang asal barang"],
    ["• Memo: Catatan tambahan untuk transaksi"],
    [""],
    ["CATATAN PENTING:"],
    ["• ID Transaksi akan dibuat otomatis oleh sistem"],
    ["• Produk harus sudah ada di sistem sebelum import"],
    ["• Stok akan otomatis berkurang sesuai quantity penjualan"],
    ["• Pelanggan baru akan dibuat otomatis jika belum ada"],
    ["• Gudang harus sudah ada di sistem"],
    ["• Total akan dihitung otomatis: (Quantity × Harga) - Diskon + PPN"],
    ["• Pastikan stok mencukupi sebelum import"],
    [""],
    ["TIPS IMPORT:"],
    ["• Periksa stok produk sebelum import untuk menghindari overselling"],
    ["• Gunakan format tanggal yang konsisten"],
    ["• Pastikan nama produk sesuai dengan yang ada di sistem"],
    ["• Backup data sebelum melakukan import besar"],
    ["• Import dalam batch kecil untuk performa optimal"],
    ["• Verifikasi harga jual sesuai dengan kebijakan terkini"],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(instructions);

  // Apply styles
  applyCellStyle(worksheet, "A1", {
    ...CELL_STYLES.reportTitle,
    font: { ...CELL_STYLES.reportTitle.font, size: 16 },
  });

  // Style section headers
  const sectionHeaders = [3, 9, 12, 16, 25, 31, 38];
  sectionHeaders.forEach((row) => {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.sectionHeader);
  });

  // Set column width
  setColumnWidths(worksheet, [80]);

  return worksheet;
};

// Get column description for sales
const getSalesColumnDescription = (column: any): string => {
  switch (column.key) {
    case "Tanggal Penjualan":
      return "Tanggal transaksi penjualan (format: YYYY-MM-DD)";
    case "Pelanggan":
      return "Nama pelanggan/customer";
    case "No. Faktur":
      return "Nomor faktur penjualan";
    case "Nama Produk":
      return "Nama produk yang dijual (harus sudah ada di sistem)";
    case "Quantity":
      return "Jumlah barang yang dijual";
    case "Satuan":
      return "Unit satuan barang (Kg, Box, Pcs, dll)";
    case "Harga Jual":
      return "Harga jual per unit dalam rupiah";
    case "Diskon (%)":
      return "Diskon dalam persentase (0-100)";
    case "Diskon (Rp)":
      return "Diskon dalam rupiah";
    case "PPN (%)":
      return "Pajak PPN dalam persentase";
    case "Harga Grosir":
      return "Gunakan harga grosir (Ya/Tidak)";
    case "Gudang":
      return "Nama gudang asal barang";
    case "Memo":
      return "Catatan tambahan untuk transaksi";
    default:
      return "Deskripsi tidak tersedia";
  }
};
